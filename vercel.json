{"rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/service-worker.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/(.*)\\.(js|css|jpg|jpeg|png|gif|ico|svg|webp|woff|woff2|ttf|otf)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "trailingSlash": false}