#!/usr/bin/env node

const https = require('https');

const testData = {
  token: '709ccd34237c5c8b23f61cb779d59f8f3bb538023eda981c272e0dce83aa4a67',
  email: '<EMAIL>',
  password: 'ServiceProvider123!',
  first_name: '<PERSON>',
  last_name: '<PERSON>',
  role: 'service_provider'
};

const postData = JSON.stringify(testData);

const options = {
  hostname: 'pwaeknalhosfwuxkpaet.supabase.co',
  port: 443,
  path: '/functions/v1/accept-invitation-direct',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'
  }
};

console.log('Testing Edge Function with data:', testData);
console.log('Making request to:', `https://${options.hostname}${options.path}`);

const req = https.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Response Body:', data);
    try {
      const jsonResponse = JSON.parse(data);
      console.log('Parsed Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Failed to parse JSON response');
    }
  });
});

req.on('error', (error) => {
  console.error('Request Error:', error);
});

req.write(postData);
req.end();
