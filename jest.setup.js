// Mock Vite environment variables
class ImportMetaEnv {
  VITE_SUPABASE_URL = 'http://localhost:3000'
  VITE_SUPABASE_ANON_KEY = 'test-anon-key'
  VITE_API_URL = 'http://localhost:3000/api'
  MODE = 'test'
  DEV = true
}

const env = new ImportMetaEnv();

// Set up import.meta
if (typeof globalThis.import === 'undefined') {
  globalThis.import = {};
}

globalThis.import.meta = {
  env,
  hot: {
    accept: () => {},
    dispose: () => {},
    invalidate: () => {},
    on: () => {},
  },
  url: '',
};

// Also add to process.env for compatibility
process.env = {
  ...process.env,
  ...env
};

// Polyfill for TextEncoder/TextDecoder
global.TextEncoder = require('util').TextEncoder;
global.TextDecoder = require('util').TextDecoder;

// Mock for window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock for ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock for window.scrollTo
window.scrollTo = jest.fn();

// Add @testing-library/jest-dom matchers
import '@testing-library/jest-dom';