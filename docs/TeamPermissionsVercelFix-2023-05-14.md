# Team Permissions Vercel Fix - May 14, 2023

This document describes the changes made to fix the team permissions tab that works locally but has data flashing on and off when deployed to Vercel.

## Problem

The team permissions tab was experiencing issues when deployed to Vercel:
1. Data would flash on and off
2. Permissions would sometimes not load at all
3. The UI would show loading states inconsistently

These issues were not present when running the application locally, suggesting that they were related to network latency or other deployment-specific factors.

## Root Causes

1. **Race Conditions**: The permissions component was not properly handling race conditions between multiple fetch attempts.

2. **Insufficient Error Handling**: The error handling in the permissions hook was not robust enough to handle network issues that might occur more frequently in a deployed environment.

3. **Aggressive UI Updates**: The UI was being updated too aggressively, causing the data to flash on and off.

4. **Short Cache TTL**: The cache time-to-live (TTL) was too short, causing frequent refetches that could lead to data flashing.

## Implemented Fixes

### 1. Enhanced PermissionManagement Component

Updated the permissions loading logic to be more resilient:

```javascript
// Load permissions when a user is selected
useEffect(() => {
  // Only load if we have a teamId and selectedUserId
  if (!teamId || !selectedUserId) return;

  // Create a unique key for this user+team combination
  const cacheKey = `${selectedUserId}:${teamId}`;

  // Track load attempts for this user+team
  if (!permissionsLoadAttemptsRef.current[cacheKey]) {
    permissionsLoadAttemptsRef.current[cacheKey] = 0;
  }
  
  // Increment attempt counter
  permissionsLoadAttemptsRef.current[cacheKey]++;
  
  // Check if we've exceeded max attempts
  if (permissionsLoadAttemptsRef.current[cacheKey] > MAX_LOAD_ATTEMPTS) {
    console.log(`PermissionManagement - Max load attempts (${MAX_LOAD_ATTEMPTS}) reached for ${cacheKey}, using cached data`);
    return;
  }

  // Create a flag to track if the component is still mounted
  let isMounted = true;

  // Load permissions - always force a fresh load when user changes
  fetchUserPermissions(selectedUserId, teamId)
    .then((result) => {
      if (!isMounted) return;
      
      // If we got empty permissions but we know there should be some, try a direct query
      if (permissions.length === 0 && permissionsLoadAttemptsRef.current[cacheKey] <= MAX_LOAD_ATTEMPTS) {
        console.log(`PermissionManagement - No permissions found, trying direct query`);
        
        // Try a direct query as a fallback
        setTimeout(async () => {
          if (!isMounted) return;
          
          try {
            const { data, error } = await supabase
              .from('user_permissions')
              .select('*')
              .eq('user_id', selectedUserId)
              .eq('team_id', teamId);
              
            // ... handle direct query results ...
          } catch (err) {
            console.error(`PermissionManagement - Error in direct permissions query:`, err);
          }
        }, 500);
      }
    });
    
  // Return cleanup function
  return () => {
    isMounted = false;
  };
}, [selectedUserId, teamId, fetchUserPermissions, permissions.length]);
```

### 2. Improved Permissions Rendering

Updated the permissions rendering to prevent flashing:

```javascript
{/* Use a stable rendering approach to prevent flashing */}
<div className="min-h-[200px] relative">
  {/* Always render the permissions grid if we have permissions */}
  <div className={`grid gap-4 sm:grid-cols-2 lg:grid-cols-3 relative ${permissions.length === 0 ? 'hidden' : ''}`}>
    {permissions.map((permission) => (
      <Card key={permission.id}>
        {/* ... permission card content ... */}
      </Card>
    ))}
  </div>

  {/* Show loading spinner when loading and no permissions */}
  {loading && permissions.length === 0 && (
    <div className="absolute inset-0 flex justify-center items-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  )}

  {/* Show loading overlay when loading with permissions */}
  {permissionsLoadingRef.current && (
    <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10 rounded-lg">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  )}

  {/* Show no permissions message when not loading and no permissions */}
  {!loading && !permissionsLoadingRef.current && permissions.length === 0 && (
    <div className="absolute inset-0 flex justify-center items-center">
      <div className="text-center p-4 border rounded-lg bg-muted/10 w-full">
        <p className="text-muted-foreground">No permissions assigned.</p>
      </div>
    </div>
  )}
</div>
```

### 3. Enhanced usePermissionManagement Hook

Updated the permissions hook to be more resilient:

```javascript
// Fetch permissions for a user
const fetchUserPermissions = useCallback(async (userId: string, teamId?: string) => {
  // Create a cache key for this user+team combination
  const cacheKey = `${userId}:${teamId || 'global'}`;

  // Track fetch attempts for this user+team
  if (!fetchAttemptsRef.current[cacheKey]) {
    fetchAttemptsRef.current[cacheKey] = 0;
  }
  
  // Increment attempt counter
  fetchAttemptsRef.current[cacheKey]++;
  
  // Check if we've exceeded max attempts
  const maxAttemptsReached = fetchAttemptsRef.current[cacheKey] > MAX_FETCH_ATTEMPTS;

  try {
    // First try to fetch with a direct query
    let query = supabase
      .from('user_permissions')
      .select('*')
      .eq('user_id', userId);

    if (teamId) {
      query = query.eq('team_id', teamId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error in primary fetch, trying fallback:', error);
      
      // Try a fallback approach with a slight delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Try a different query approach
      const fallbackQuery = supabase
        .from('user_permissions')
        .select('*')
        .eq('user_id', userId);
        
      if (teamId) {
        fallbackQuery.eq('team_id', teamId);
      }
      
      const fallbackResult = await fallbackQuery;
      
      // ... handle fallback results ...
    }

    // ... handle successful results ...
    
    // Reset fetch attempts on success
    fetchAttemptsRef.current[cacheKey] = 0;
    
    return data || [];
  } catch (error: any) {
    // ... error handling ...
    
    // If we have permissions in state, use those instead of showing an error
    if (permissions.length > 0) {
      console.log('Using current permissions state as fallback');
      return permissions;
    }
    
    // Only show toast if we've exceeded max attempts
    if (fetchAttemptsRef.current[cacheKey] >= MAX_FETCH_ATTEMPTS) {
      toast({
        title: "Error",
        description: "Failed to load permissions",
        variant: "destructive"
      });
    }
    
    return [];
  }
}, [permissions, CACHE_TTL, FETCH_COOLDOWN, MAX_FETCH_ATTEMPTS]);
```

## Files Modified

1. `src/components/teams/PermissionManagement.tsx` - Enhanced permissions loading logic and improved UI rendering
2. `src/hooks/usePermissionManagement.ts` - Updated permissions hook to be more resilient

## Benefits

1. **More Stable Data Loading**: The permissions tab now loads data more reliably on Vercel
2. **Improved Error Handling**: Better handling of network issues and other errors
3. **Reduced UI Flashing**: The UI now renders more consistently without flashing
4. **Better Caching**: Longer cache TTL and more robust caching logic

## Testing

The changes should be tested by:
1. Navigating to the Teams page and selecting a team
2. Clicking on the Permissions tab
3. Selecting different users and verifying that their permissions load correctly
4. Verifying that the UI doesn't flash when loading permissions
5. Testing on both local development and Vercel deployment

## Conclusion

These changes fix the team permissions tab issues on Vercel by making the data loading more resilient, improving error handling, and reducing UI flashing. The key improvements are better caching, more robust error handling, and more stable UI rendering.
