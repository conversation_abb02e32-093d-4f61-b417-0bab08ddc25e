
import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import PageTransition from '../components/layout/PageTransition';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { mapPropertyToCardProperty } from '@/utils/propertyUtils';
import { useAuth } from '@/contexts/AuthContext';
import { Property as CardProperty, CollectionWithBudget } from '@/components/properties/PropertyCard';
import { usePendingItems } from '@/hooks/usePendingItems';
import BookingCalendar from '@/components/properties/calendar';
import PropertyDetailHeader from '@/components/properties/PropertyDetailHeader';
import PropertySummary from '@/components/properties/PropertySummary';
import PropertyOverviewTab from '@/components/properties/PropertyOverviewTab';
import PropertyPendingItems from '@/components/properties/PropertyPendingItems';
import CollectionsTab from '@/components/properties/CollectionsTab';
import PropertyEditForm from '@/components/properties/PropertyEditForm';
import usePropertyDetail from '@/hooks/usePropertyDetail';
import PropertyCollectionsManager from '@/components/properties/PropertyCollectionsManager';
import PropertyDocumentsTab from '@/components/properties/PropertyDocumentsTab';
import usePropertyUpdate from '@/hooks/usePropertyUpdate';
import PropertyActions from '@/components/properties/PropertyActions';

const PropertyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const userId = authState.user?.id;

  // Add state to track session check
  const [isCheckingSession, setIsCheckingSession] = useState(false);
  const [sessionError, setSessionError] = useState<string | null>(null);
  const sessionCheckedRef = useRef(false);

  const { property, loading, error, setProperty, fetchProperty } = usePropertyDetail(id, userId);
  const { pendingItems, loading: pendingLoading } = usePendingItems(id, userId);
  const { handleUpdateProperty } = usePropertyUpdate();

  // Check and refresh session before loading property data
  useEffect(() => {
    const checkSession = async () => {
      // Skip if we've already checked the session
      if (sessionCheckedRef.current) return;

      try {
        setIsCheckingSession(true);
        console.log('[PropertyDetail] Checking session validity');

        // Get the current session
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('[PropertyDetail] Session check error:', error);
          setSessionError(`Session error: ${error.message}`);
        } else if (!data.session) {
          console.warn('[PropertyDetail] No active session found');
          setSessionError('No active session found');
        } else {
          console.log('[PropertyDetail] Valid session found, proceeding with data load');
          sessionCheckedRef.current = true;
        }
      } catch (e) {
        console.error('[PropertyDetail] Error checking session:', e);
        setSessionError(`Error checking session: ${e instanceof Error ? e.message : String(e)}`);
      } finally {
        setIsCheckingSession(false);
      }
    };

    // Only check session if we're authenticated
    if (authState?.isAuthenticated && !sessionCheckedRef.current) {
      checkSession();
    }
  }, [authState?.isAuthenticated]);

  const [isEditMode, setIsEditMode] = useState(false);
  const [editProperty, setEditProperty] = useState<CardProperty | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const handleEdit = () => {
    setIsEditMode(true);
    if (property) {
      setEditProperty(mapPropertyToCardProperty(property));
    }
  };

  const handleSaveEdit = () => {
    if (property && editProperty) {
      handleUpdateProperty(id, property, editProperty as any).then(data => {
        if (data) {
          setProperty(prev => prev ? {
            ...prev,
            ...data,
            collections: data.collections || prev.collections
          } : null);
        }
        setIsEditMode(false);
      });
    }
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
  };

  const handleManageCollections = () => {
    setActiveTab('collections');
  };

  const handleCollectionsChange = (updatedCollections: CollectionWithBudget[]) => {
    if (!property) return;

    // Create a property update with just the collections
    const propertyUpdate = {
      id: property.id,
      user_id: userId,
      collections: updatedCollections
    };

    handleUpdateProperty(property.id, property, propertyUpdate as any).then(data => {
      if (data) {
        setProperty(prev => prev ? {
          ...prev,
          collections: data.collections || prev.collections
        } : null);
      }
    });
  }

  // Track loading time to show additional UI for long loads
  const [loadingTime, setLoadingTime] = useState(0);
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Set up a timer to track loading time
  useEffect(() => {
    if (loading || isCheckingSession) {
      // Start the timer
      const startTime = Date.now();
      loadingTimerRef.current = setInterval(() => {
        setLoadingTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    } else {
      // Clear the timer when loading is done
      if (loadingTimerRef.current) {
        clearInterval(loadingTimerRef.current);
        loadingTimerRef.current = null;
      }
      setLoadingTime(0);
    }

    // Cleanup
    return () => {
      if (loadingTimerRef.current) {
        clearInterval(loadingTimerRef.current);
      }
    };
  }, [loading, isCheckingSession]);

  // Show loading state for session check
  if (isCheckingSession) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[50vh]">
          <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
          <p className="text-muted-foreground">Verifying session...</p>

          {/* Show additional UI if loading takes too long */}
          {loadingTime > 5 && (
            <div className="mt-8 text-center">
              <p className="text-sm text-muted-foreground mb-2">
                This is taking longer than expected ({loadingTime}s)
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Force refresh the session
                  supabase.auth.getSession();
                  // Reset the session check flag
                  sessionCheckedRef.current = false;
                }}
                className="mt-2"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Session Check
              </Button>
            </div>
          )}
        </div>
      </PageTransition>
    );
  }

  // Show session error state
  if (sessionError) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-8">
          <div className="glass rounded-xl p-8 text-center">
            <h2 className="text-2xl font-bold mb-4 text-destructive">Session Error</h2>
            <p className="text-muted-foreground mb-6">There was a problem with your session. Please try refreshing the page or logging in again.</p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => {
                  // Try to refresh the session
                  supabase.auth.getSession().then(() => {
                    // Reset the session error and check flag
                    setSessionError(null);
                    sessionCheckedRef.current = false;
                  });
                }}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh Session
              </Button>

              <Button onClick={() => navigate('/login')}>
                Log In Again
              </Button>
            </div>

            {/* Show technical details for debugging */}
            <div className="mt-8 text-left text-xs text-muted-foreground border-t pt-4">
              <p className="font-semibold mb-1">Technical Details:</p>
              <p>Error: {sessionError}</p>
              <p>User ID: {userId || 'Not available'}</p>
              <p>Time: {new Date().toISOString()}</p>
            </div>
          </div>
        </div>
      </PageTransition>
    );
  }

  if (loading) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[50vh]">
          <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
          <p className="text-muted-foreground">Loading property details...</p>

          {/* Show additional UI if loading takes too long */}
          {loadingTime > 5 && (
            <div className="mt-8 text-center">
              <p className="text-sm text-muted-foreground mb-2">
                This is taking longer than expected ({loadingTime}s)
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Force refresh the property data
                  fetchProperty();
                  // Also try to refresh the session
                  supabase.auth.getSession();
                }}
                className="mt-2"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Loading
              </Button>
            </div>
          )}
        </div>
      </PageTransition>
    );
  }

  if (error || !property) {
    return (
      <PageTransition>
        <div className="container mx-auto px-4 py-8">
          <div className="glass rounded-xl p-8 text-center">
            <h2 className="text-2xl font-bold mb-4 text-destructive">Error</h2>
            <p className="text-muted-foreground mb-6">{error || 'Property not found'}</p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => {
                  // Try to refresh the session first
                  supabase.auth.getSession().then(() => {
                    // Then retry loading the property
                    fetchProperty();
                  });
                }}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Retry Loading
              </Button>

              <Button onClick={() => navigate('/properties')}>
                Return to Properties
              </Button>
            </div>

            {/* Show technical details for debugging */}
            <div className="mt-8 text-left text-xs text-muted-foreground border-t pt-4">
              <p className="font-semibold mb-1">Technical Details:</p>
              <p>Property ID: {id}</p>
              <p>User ID: {userId || 'Not available'}</p>
              <p>Error: {error || 'Property not found'}</p>
              <p>Time: {new Date().toISOString()}</p>
            </div>
          </div>
        </div>
      </PageTransition>
    );
  }

  if (isEditMode) {
    return (
      <PageTransition>
        <PropertyEditForm
          property={property}
          editProperty={editProperty}
          setEditProperty={setEditProperty}
          onSave={handleSaveEdit}
          onCancel={handleCancelEdit}
          userId={userId}
        />
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="container mx-auto px-4 py-8 pb-32">
        <PropertyDetailHeader
          property={property}
          onEdit={handleEdit}
        />

        <PropertySummary property={property} />

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="collections">Collections</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="pending">Pending Items</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <PropertyOverviewTab
              property={property}
              userId={userId}
              onSyncComplete={fetchProperty}
              onManageCollections={handleManageCollections}
            />
          </TabsContent>

          <TabsContent value="collections">
            <PropertyCollectionsManager
              propertyId={property.id}
              collections={property.collections || []}
              onCollectionsChange={handleCollectionsChange}
            />
          </TabsContent>

          <TabsContent value="documents">
            <PropertyDocumentsTab
              propertyId={property.id}
              isPropertyOwner={property.user_id === userId}
            />
          </TabsContent>

          <TabsContent value="pending">
            <PropertyPendingItems
              pendingItems={pendingItems}
              propertyId={property.id}
              loading={pendingLoading}
            />
          </TabsContent>
        </Tabs>

        {property && <BookingCalendar propertyId={property.id} />}
      </div>
    </PageTransition>
  );
};

export default PropertyDetail;
