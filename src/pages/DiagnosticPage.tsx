import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

const DiagnosticPage: React.FC = () => {
  const [diagnostics, setDiagnostics] = useState<Record<string, any>>({});
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  useEffect(() => {
    // Environment diagnostics - synchronous only
    const envDiagnostics = {
      mode: import.meta.env?.MODE || 'unknown',
      dev: import.meta.env?.DEV || false,
      prod: import.meta.env?.PROD || false,
      supabaseUrl: import.meta.env?.VITE_SUPABASE_URL || 'NOT_SET',
      supabaseKeySet: !!import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY,
      supabaseKeyPrefix: import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY?.substring(0, 20) || 'NOT_SET',
      actualUrl: supabase?.supabaseUrl || 'NOT_AVAILABLE',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      location: window.location.href,
    };

    setDiagnostics(envDiagnostics);

    // Simple async tests
    const runAsyncTests = async () => {
      // Test basic Supabase connection
      try {
        console.log('Testing basic Supabase connection...');
        const { data, error } = await supabase.from('profiles').select('id').limit(1);
        setTestResults(prev => ({
          ...prev,
          supabaseConnection: {
            success: !error,
            error: error?.message || null,
            hasData: !!data,
            timestamp: new Date().toISOString()
          }
        }));
      } catch (err) {
        console.error('Supabase connection error:', err);
        setTestResults(prev => ({
          ...prev,
          supabaseConnection: {
            success: false,
            error: (err as Error).message,
            timestamp: new Date().toISOString()
          }
        }));
      }
    };

    runAsyncTests();
  }, []);

  const testEdgeFunction = async () => {
    try {
      console.log('Testing Edge Function manually...');
      const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
        body: JSON.stringify({
          token: 'test-token',
          email: '<EMAIL>',
          password: 'testpass',
          first_name: 'Test',
          last_name: 'User'
        })
      });
      setTestResults(prev => ({
        ...prev,
        edgeFunctionManual: {
          success: !error,
          error: error?.message || null,
          data: data,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (err) {
      console.error('Edge Function test error:', err);
      setTestResults(prev => ({
        ...prev,
        edgeFunctionManual: {
          success: false,
          error: (err as Error).message,
          timestamp: new Date().toISOString()
        }
      }));
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🔍 Diagnostic Page</h1>

      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Environment Diagnostics</h2>
          <pre className="text-sm overflow-auto bg-white p-2 rounded border">
            {JSON.stringify(diagnostics, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Test Results</h2>
          <pre className="text-sm overflow-auto bg-white p-2 rounded border">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>

        <div className="bg-blue-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Manual Tests</h2>
          <button
            type="button"
            onClick={testEdgeFunction}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Test Edge Function
          </button>
        </div>

        <div className="bg-yellow-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Console Output</h2>
          <p className="text-sm">Check the browser console for detailed logs from the Supabase client configuration.</p>
        </div>
      </div>
    </div>
  );
};

export default DiagnosticPage;
