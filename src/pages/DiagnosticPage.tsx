import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

const DiagnosticPage = () => {
  const [diagnostics, setDiagnostics] = useState<any>({});
  const [testResults, setTestResults] = useState<any>({});

  useEffect(() => {
    const runDiagnostics = async () => {
      // Environment diagnostics
      const envDiagnostics = {
        mode: import.meta.env?.MODE,
        dev: import.meta.env?.DEV,
        prod: import.meta.env?.PROD,
        supabaseUrl: import.meta.env?.VITE_SUPABASE_URL,
        supabaseKeySet: !!import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY,
        supabaseKeyPrefix: import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY?.substring(0, 20),
        actualUrl: supabase.supabaseUrl,
        actualKeyPrefix: (supabase as any).supabaseKey?.substring(0, 20),
      };

      setDiagnostics(envDiagnostics);

      // Test Supabase connection
      try {
        console.log('Testing Supabase connection...');
        const { data, error } = await supabase.from('profiles').select('count').limit(1);
        setTestResults(prev => ({
          ...prev,
          supabaseConnection: { success: !error, error: error?.message, data }
        }));
      } catch (err) {
        setTestResults(prev => ({
          ...prev,
          supabaseConnection: { success: false, error: (err as Error).message }
        }));
      }

      // Test Edge Function
      try {
        console.log('Testing Edge Function...');
        const { data, error } = await supabase.functions.invoke('accept-invitation-direct', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token: 'test-token',
            email: '<EMAIL>',
            password: 'testpass',
            first_name: 'Test',
            last_name: 'User'
          })
        });
        setTestResults(prev => ({
          ...prev,
          edgeFunction: { success: !error, error: error?.message, data }
        }));
      } catch (err) {
        setTestResults(prev => ({
          ...prev,
          edgeFunction: { success: false, error: (err as Error).message }
        }));
      }
    };

    runDiagnostics();
  }, []);

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Diagnostic Page</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Environment Diagnostics</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(diagnostics, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Test Results</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>

        <div className="bg-yellow-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-3">Console Output</h2>
          <p className="text-sm">Check the browser console for detailed logs from the Supabase client configuration.</p>
        </div>
      </div>
    </div>
  );
};

export default DiagnosticPage;
