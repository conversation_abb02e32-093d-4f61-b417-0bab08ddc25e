import React, { ErrorInfo, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import { OnboardingProvider } from '@/contexts/OnboardingContext';
import { GlobalDataRefreshProvider } from '@/contexts/GlobalDataRefreshContext';
import { Toaster } from 'sonner';
import ThemeInitializer from '@/components/theme/ThemeInitializer';
import { setGlobalQueryClient } from '@/utils/cacheUtils';

class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return null; // Or a fallback UI if you prefer
    }

    return this.props.children;
  }
}

const AppProviders = ({ children }: { children: React.ReactNode }) => {
  const [queryClient] = React.useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: true, // Changed back to true to enable data refreshing when window regains focus
            refetchOnMount: true,
            refetchOnReconnect: true,
            retry: 3,
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            staleTime: 1000 * 60 * 5, // 5 minutes
            gcTime: 30 * 60 * 1000, // 30 minutes
            networkMode: 'always'
          },
          mutations: {
            retry: 2,
            networkMode: 'always',
          },
        },
      })
  );

  useEffect(() => {
    setGlobalQueryClient(queryClient);
  }, [queryClient]);

  return (
    <ErrorBoundary>
      <React.Suspense fallback={null}>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <GlobalDataRefreshProvider>
              <OnboardingProvider>
                <ThemeInitializer />
                <Toaster
                  position="top-right"
                  richColors
                  closeButton
                  duration={1000}
                  toastOptions={{
                    // Default options for all toasts
                    duration: 1000, // 1 second default duration
                  }}
                />
                {children}
              </OnboardingProvider>
            </GlobalDataRefreshProvider>
          </AuthProvider>
        </QueryClientProvider>
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default AppProviders;
