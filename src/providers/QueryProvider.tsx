
import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true, // Enable refetch on window focus for data refreshing after idle
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
      staleTime: 1000 * 60 * 5, // 5 minutes stale time to prevent excessive refetching
      cacheTime: 1000 * 60 * 10, // 10 minutes cache time
      refetchOnMount: true, // Keep true to refresh data when component mounts
      refetchOnReconnect: true,
      keepPreviousData: true, // Keep previous data while fetching new data
    },
  },
});

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};
