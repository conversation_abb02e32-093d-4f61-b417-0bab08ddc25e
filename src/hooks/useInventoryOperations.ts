
import { useState } from 'react';
import { useInventoryQueryV2 } from './useInventoryQueryV2';
import { FormattedInventoryItem } from '@/types/inventory';
import { useAuth } from '@/contexts/AuthContext';
import { useDialog } from './useDialog';
import { toast } from 'sonner';

export const useInventoryOperations = () => {
  const [selectedItem, setSelectedItem] = useState<Partial<FormattedInventoryItem> | null>(null);
  const { addInventoryItem, updateInventoryItem, deleteInventoryItem, bulkAddInventoryItems } = useInventoryQueryV2();
  const { authState } = useAuth();

  // Dialog controls
  const itemDialog = useDialog();
  const purchaseOrderDialog = useDialog();
  const bulkImportDialog = useDialog();

  // Handle clicking an item to edit
  const handleItemClick = (item: FormattedInventoryItem) => {
    // Map from database format to form format if needed
    const formattedItem: FormattedInventoryItem = {
      ...item,
      propertyId: item.propertyId,
      minQuantity: item.minQuantity,
      amazonUrl: item.amazonUrl,
      walmartUrl: item.walmartUrl,
      imageUrl: item.imageUrl
    };

    setSelectedItem(formattedItem);
    itemDialog.onOpen();
  };

  // Handle adding a new item
  const handleAddItem = () => {
    setSelectedItem({
      name: '',
      propertyId: '',
      collection: '',
      quantity: 0,
      minQuantity: 1,
      price: undefined,
      amazonUrl: '',
      walmartUrl: '',
      imageUrl: ''
    });
    itemDialog.onOpen();
  };

  // Handle creating a purchase order
  const handleCreatePurchaseOrder = () => {
    purchaseOrderDialog.onOpen();
  };

  // Handle bulk import
  const handleBulkImport = () => {
    bulkImportDialog.onOpen();
  };

  // Handle saving an item (new or edit)
  const handleSaveItem = async (item: FormattedInventoryItem) => {
    if (!authState.user) {
      toast.error('You must be logged in to save inventory items');
      return;
    }

    const saveItem = {
      id: item.id,
      name: item.name,
      propertyId: item.propertyId,
      collection: item.collection,
      quantity: item.quantity,
      minQuantity: item.minQuantity,
      price: item.price,
      amazonUrl: item.amazonUrl,
      walmartUrl: item.walmartUrl,
      imageUrl: item.imageUrl,
      hasProcessedImage: item.hasProcessedImage // Flag to track if image is already processed
    };

    try {
      if (item.id) {
        // Update existing item
        const success = await updateInventoryItem(item.id, saveItem);
        if (success) {
          toast.success('Item updated successfully');
          itemDialog.onClose();
          setSelectedItem(null);
        } else {
          toast.error('Failed to update item');
        }
      } else {
        // Check if the image URL is an object URL (blob:)
        const isObjectUrl = saveItem.imageUrl?.startsWith('blob:');

        // If it's an object URL, we need to handle it differently
        // For now, we'll just log it and use it as is
        if (isObjectUrl) {
          console.log('Using object URL for image:', saveItem.imageUrl);
          toast.info('Using temporary image URL. The image may not persist after page refresh.');
        }

        // Add new item
        const newItem = {
          name: saveItem.name,
          propertyId: saveItem.propertyId,
          collection: saveItem.collection,
          quantity: saveItem.quantity,
          minQuantity: saveItem.minQuantity,
          price: saveItem.price,
          amazonUrl: saveItem.amazonUrl,
          walmartUrl: saveItem.walmartUrl,
          imageUrl: saveItem.imageUrl,
          hasProcessedImage: !isObjectUrl && saveItem.hasProcessedImage
        };

        const success = await addInventoryItem(newItem);
        if (success) {
          toast.success('Item added successfully');
          itemDialog.onClose();
          setSelectedItem(null);
        } else {
          toast.error('Failed to add item');
        }
      }
    } catch (error) {
      console.error('Error saving item:', error);
      toast.error('Failed to save item');
    }
  };

  // Handle deleting one or more items
  const handleDeleteItems = async (itemIds: string[]) => {
    if (itemIds.length === 1) {
      try {
        const success = await deleteInventoryItem(itemIds[0]);
        if (success) {
          toast.success('Item deleted successfully');
        } else {
          toast.error('Failed to delete item');
        }
      } catch (error) {
        console.error('Error deleting item:', error);
        toast.error('Failed to delete item');
      }
    } else if (itemIds.length > 1) {
      // Delete multiple items sequentially
      let deletedCount = 0;
      const totalCount = itemIds.length;

      // Create a loading toast that will be updated
      const loadingToast = toast.loading(`Deleting ${totalCount} items...`);

      // Process deletions sequentially
      const deleteNext = async (index: number) => {
        if (index >= itemIds.length) {
          toast.dismiss(loadingToast);
          toast.success(`Successfully deleted ${deletedCount} items`);
          return;
        }

        try {
          const success = await deleteInventoryItem(itemIds[index]);
          if (success) {
            deletedCount++;
          } else {
            console.error(`Failed to delete item ${itemIds[index]}`);
          }
        } catch (error) {
          console.error(`Failed to delete item ${itemIds[index]}:`, error);
        }

        // Continue with next item regardless of success/failure
        deleteNext(index + 1);
      };

      deleteNext(0);
    }
  };

  // Handle bulk saving items
  const handleBulkSaveItems = async (items: Partial<FormattedInventoryItem>[]) => {
    if (!authState.user) {
      toast.error('You must be logged in to import items');
      return;
    }

    // Format items for bulk import
    const formattedItems = items.map(item => ({
      name: item.name || 'Unnamed Product',
      propertyId: item.propertyId || '',
      collection: item.collection || 'Other',
      quantity: item.quantity || 1,
      minQuantity: item.minQuantity || 0,
      price: item.price,
      amazonUrl: item.amazonUrl || '',
      walmartUrl: item.walmartUrl || '',
      imageUrl: item.imageUrl || '',
      hasProcessedImage: item.hasProcessedImage || false
    }));

    try {
      const success = await bulkAddInventoryItems(formattedItems as FormattedInventoryItem[]);
      if (success) {
        toast.success('Items imported successfully');
        bulkImportDialog.onClose();
      } else {
        toast.error('Failed to import items');
      }
    } catch (error) {
      console.error('Error importing items:', error);
      toast.error('Failed to import items');
    }
  };

  return {
    selectedItem,
    itemDialog,
    purchaseOrderDialog,
    bulkImportDialog,
    handleItemClick,
    handleAddItem,
    handleCreatePurchaseOrder,
    handleBulkImport,
    handleSaveItem,
    handleDeleteItems,
    handleBulkSaveItems,
  };
};
