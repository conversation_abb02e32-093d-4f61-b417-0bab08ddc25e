import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import { Property } from '@/types/properties';

/**
 * A simplified hook for fetching properties using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const usePropertiesQueryV2 = () => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const { isImpersonating, applyImpersonationFilter } = useImpersonation();

  // Function to process property data
  const processPropertiesData = (data: any[]): Property[] => {
    return data.map(property => ({
      id: property.id,
      name: property.name || 'Unnamed Property',
      address: property.address || '',
      city: property.city || '',
      state: property.state || '',
      zip: property.zip || '',
      image_url: property.image_url || '',
      bedrooms: typeof property.bedrooms === 'number' ? property.bedrooms : 1,
      bathrooms: typeof property.bathrooms === 'number' ? property.bathrooms : 1,
      budget: typeof property.budget === 'number' ? property.budget : 0,
      ical_url: property.ical_url || '',
      next_booking: property.next_booking || '',
      next_checkin_date: property.next_checkin_date || '',
      next_checkin_formatted: property.next_checkin_formatted || '',
      collections: property.collections || [],
      is_occupied: !!property.is_occupied,
      current_checkout: property.current_checkout || '',
      last_ical_sync: property.last_ical_sync || '',
      timezone: property.timezone || 'America/Los_Angeles',
      check_in_time: property.check_in_time || '15:00:00',
      check_out_time: property.check_out_time || '11:00:00',
    }));
  };

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[usePropertiesQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['propertiesV2'] });
    await queryClient.refetchQueries({ queryKey: ['propertiesV2'] });
  }, [queryClient]);

  // Fetch properties using React Query
  const {
    data: properties = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['propertiesV2', retryCount],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[usePropertiesQueryV2] Fetching properties (attempt ${retryCount + 1})`);

        // Use the unified get_user_role_properties function that handles all user roles
        const { data, error } = await supabase.rpc(
          'get_user_role_properties',
          { p_user_id: userId }
        );

        if (error) {
          console.error('[usePropertiesQueryV2] RPC function error:', error);
          throw error;
        }

        if (!data) {
          console.log('[usePropertiesQueryV2] No properties data returned');
          return [];
        }

        console.log(`[usePropertiesQueryV2] Successfully loaded ${data.length} properties via RPC function`);

        // Apply impersonation filter if needed
        let filteredData = data;
        if (isImpersonating) {
          console.log('[usePropertiesQueryV2] Impersonating user, filtering properties');
          // Simple filter for impersonation - can be enhanced if needed
          filteredData = data.filter(p => p.user_id === userId);
        }

        // Process the properties and return them
        return processPropertiesData(filteredData);
      } catch (err: any) {
        console.error('[usePropertiesQueryV2] Error fetching properties:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes - same as Operations page
    refetchOnWindowFocus: true, // Keep this true to ensure data refreshes when the page regains focus
    refetchOnMount: true,
    enabled: !!userId
  });

  // Add error retry effect similar to usePurchaseOrders
  useEffect(() => {
    // Only retry on actual errors, not on empty data
    if (isError) {
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[usePropertiesQueryV2] Auto-retrying data fetch due to error');
          retryFetch();
        } else {
          console.error('[usePropertiesQueryV2] Failed to load properties after multiple attempts');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, retryFetch]);

  return {
    properties,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchProperties: retryFetch
  };
};
