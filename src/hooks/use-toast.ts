import * as React from "react"
import { toast as sonnerToast } from "sonner";

import type {
  ToastActionElement,
  ToastProps,
} from "@/components/ui/toast"

// Set a very short removal delay to make toasts disappear quickly
// Default was 3250ms, now we'll use 1000ms (1 second)
const TOAST_REMOVE_DELAY = 1000

// Increase the limit to allow for multiple notifications when needed
const TOAST_LIMIT = 3

type ToasterToast = ToastProps & {
  id: string
  title?: React.ReactNode
  description?: React.ReactNode
  action?: ToastActionElement
}

const actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const

let count = 0

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER
  return count.toString()
}

type ActionType = typeof actionTypes

type Action =
  | {
      type: ActionType["ADD_TOAST"]
      toast: ToasterToast
    }
  | {
      type: ActionType["UPDATE_TOAST"]
      toast: Partial<ToasterToast>
    }
  | {
      type: ActionType["DISMISS_TOAST"]
      toastId?: ToasterToast["id"]
    }
  | {
      type: ActionType["REMOVE_TOAST"]
      toastId?: ToasterToast["id"]
    }

interface State {
  toasts: ToasterToast[]
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId)
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    })
  }, TOAST_REMOVE_DELAY)

  toastTimeouts.set(toastId, timeout)
}

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      // Enhanced de-duplication logic
      // Check for duplicates with same title or similar content
      const isDuplicate = state.toasts.some(t => {
        // Exact match on title and description
        if (t.title === action.toast.title &&
            t.description === action.toast.description &&
            t.open === true) {
          return true;
        }

        // Match on title only if it's a string
        if (typeof t.title === 'string' &&
            typeof action.toast.title === 'string' &&
            t.title === action.toast.title &&
            t.open === true) {
          return true;
        }

        // For error toasts, check if they're similar
        if (action.toast.variant === 'destructive' &&
            t.variant === 'destructive' &&
            t.open === true) {
          return true;
        }

        return false;
      });

      if (isDuplicate) {
        console.log('Preventing duplicate toast:', action.toast.title);
        return state;
      }

      // Remove any existing toasts with the same ID if they exist
      const filteredToasts = state.toasts.filter(t => t.id !== action.toast.id);

      return {
        ...state,
        toasts: [action.toast, ...filteredToasts].slice(0, TOAST_LIMIT),
      }

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === action.toast.id ? { ...t, ...action.toast } : t
        ),
      }

    case "DISMISS_TOAST": {
      const { toastId } = action

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId)
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id)
        })
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t
        ),
      }
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        }
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      }
  }
}

const listeners: Array<(state: State) => void> = []

let memoryState: State = { toasts: [] }

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action)
  listeners.forEach((listener) => {
    listener(memoryState)
  })
}

type Toast = Omit<ToasterToast, "id">

// Create a fallback version of the toast function that uses sonner
// This ensures we can always display toasts even if React's useState is unavailable
function toast({ ...props }: Toast) {
  // Use the internal toast implementation if possible
  try {
    const id = genId()

    const update = (props: ToasterToast) =>
      dispatch({
        type: "UPDATE_TOAST",
        toast: { ...props, id },
      })
    const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id })

    dispatch({
      type: "ADD_TOAST",
      toast: {
        ...props,
        id,
        open: true,
        onOpenChange: (open) => {
          if (!open) dismiss()
        },
      },
    })

    return {
      id: id,
      dismiss,
      update,
    }
  } catch (e) {
    // Fallback to sonner if our implementation fails
    console.error("Error in toast implementation, falling back to sonner:", e);

    // Map our props to sonner's props format
    if (props.title && typeof props.title === 'string') {
      // If we have a title and description, use them both
      if (props.description && typeof props.description === 'string') {
        return sonnerToast(props.title, {
          description: props.description,
          duration: 1000 // 1 second duration
        });
      }
      // Otherwise just show the title
      return sonnerToast(props.title, {
        duration: 1000 // 1 second duration
      });
    }

    // If we don't have a proper title, just show a generic message
    return sonnerToast("Notification", {
      duration: 1000 // 1 second duration
    });
  }
}

function useToast() {
  // Add a defensive check to ensure React is available
  if (typeof React === 'undefined' || !React.useState) {
    console.error("React is not defined or useState is not available in useToast");
    // Return a minimal implementation that won't crash
    return {
      toasts: [],
      toast,
      dismiss: () => {}
    };
  }

  const [state, setState] = React.useState<State>(memoryState)

  React.useEffect(() => {
    listeners.push(setState)
    return () => {
      const index = listeners.indexOf(setState)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }, [state])

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  }
}

export { useToast, toast }
