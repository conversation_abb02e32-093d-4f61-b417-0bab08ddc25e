import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';
import { useState, useCallback, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

/**
 * A standardized hook for data fetching using React Query
 * This ensures consistent behavior across all components
 * 
 * @param queryKey - The key for the query (used for caching and refetching)
 * @param queryFn - The function to fetch the data
 * @param options - Additional options for the query
 * @returns The query result with additional helper functions
 */
export function useStandardQuery<TData, TError = Error>(
  queryKey: string | unknown[],
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'> & {
    showLoadingToast?: boolean;
    showErrorToast?: boolean;
    showSuccessToast?: boolean;
    loadingMessage?: string;
    errorMessage?: string;
    successMessage?: string;
  }
): UseQueryResult<TData, TError> & {
  refetch: () => Promise<void>;
  retryCount: number;
} {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const [toastId, setToastId] = useState<string | number | undefined>(undefined);

  // Extract custom options
  const {
    showLoadingToast = false,
    showErrorToast = true,
    showSuccessToast = false,
    loadingMessage = 'Loading data...',
    errorMessage = 'Failed to load data',
    successMessage = 'Data loaded successfully',
    ...queryOptions
  } = options || {};

  // Normalize query key to array
  const normalizedQueryKey = Array.isArray(queryKey) ? queryKey : [queryKey];
  
  // Add retry count to query key to force refetch
  const fullQueryKey = [...normalizedQueryKey, retryCount];

  // Create the query with standardized options
  const query = useQuery<TData, TError, TData>({
    queryKey: fullQueryKey,
    queryFn: async () => {
      // Show loading toast if enabled
      if (showLoadingToast) {
        setToastId(toast.loading(loadingMessage));
      }

      try {
        // Log the query execution
        console.log(`[useStandardQuery] Fetching data for ${normalizedQueryKey[0]} (attempt ${retryCount + 1})`);
        
        // Execute the query function
        const result = await queryFn();
        
        // Show success toast if enabled
        if (showSuccessToast) {
          if (toastId) {
            toast.dismiss(toastId);
            setToastId(undefined);
          }
          toast.success(successMessage);
        } else if (toastId) {
          // Dismiss loading toast if success toast is not shown
          toast.dismiss(toastId);
          setToastId(undefined);
        }
        
        return result;
      } catch (error) {
        // Log the error
        console.error(`[useStandardQuery] Error fetching data for ${normalizedQueryKey[0]}:`, error);
        
        // Show error toast if enabled
        if (showErrorToast) {
          if (toastId) {
            toast.dismiss(toastId);
            setToastId(undefined);
          }
          toast.error(errorMessage);
        } else if (toastId) {
          // Dismiss loading toast if error toast is not shown
          toast.dismiss(toastId);
          setToastId(undefined);
        }
        
        // Re-throw the error for React Query to handle
        throw error;
      }
    },
    // Standard options for all queries
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    cacheTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    ...queryOptions
  });

  // Manual refetch function with retry count increment
  const refetch = useCallback(async () => {
    console.log(`[useStandardQuery] Manual refetch triggered for ${normalizedQueryKey[0]}`);
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: normalizedQueryKey });
    await queryClient.refetchQueries({ queryKey: normalizedQueryKey });
  }, [queryClient, normalizedQueryKey]);

  // Auto-retry on error
  useEffect(() => {
    if (query.isError && retryCount < 3) {
      const timer = setTimeout(() => {
        console.log(`[useStandardQuery] Auto-retrying data fetch for ${normalizedQueryKey[0]} due to error`);
        refetch();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [query.isError, retryCount, refetch, normalizedQueryKey]);

  // Return the query result with additional helper functions
  return {
    ...query,
    refetch,
    retryCount
  };
}
