
import { useState, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { PurchaseOrder, PurchaseOrderItem, POStatus } from '@/types/inventory';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

export const usePurchaseOrders = () => {
  const queryClient = useQueryClient();
  const { authState } = useAuth();
  const [retryCount, setRetryCount] = useState(0);

  // Function to retry data fetching manually if needed
  const retryFetch = useCallback(() => {
    setRetryCount(prev => prev + 1);
    queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] });
  }, [queryClient]);

  // Fetch all purchase orders (including archived ones)
  const {
    data: purchaseOrders,
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['purchaseOrders', retryCount],
    queryFn: async () => {
      console.log('[usePurchaseOrders] Fetching purchase orders, attempt:', retryCount + 1);

      if (!authState.user?.id) {
        console.log('[usePurchaseOrders] No authenticated user, skipping fetch');
        return [];
      }

      // Improved error handling with proper logging
      try {
        // Step 1: Get user's own purchase orders
        const { data: ownOrders, error: ownOrdersError } = await supabase
          .from('purchase_orders')
          .select(`
            *,
            property:property_id (name)
          `)
          .eq('user_id', authState.user.id)
          .order('created_at', { ascending: false });

        if (ownOrdersError) {
          console.error('[usePurchaseOrders] Error fetching own purchase orders:', ownOrdersError);
          throw ownOrdersError;
        }

        console.log(`[usePurchaseOrders] Successfully loaded ${ownOrders?.length || 0} own purchase orders`);

        // Step 2: Get team-related purchase orders
        // Get user teams
        const { data: userTeamsData, error: teamsError } = await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', authState.user.id);

        if (teamsError) {
          console.error('[usePurchaseOrders] Error fetching user teams:', teamsError);
          throw teamsError;
        }

        const userTeams = userTeamsData?.map((t: { team_id: string }) => t.team_id) || [];
        let teamOrders: any[] = [];

        if (userTeams.length > 0) {
          // Get properties from teams the user is a member of
          const { data: teamPropertiesData, error: teamPropertiesError } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', userTeams);

          if (teamPropertiesError) {
            console.error('[usePurchaseOrders] Error fetching team properties:', teamPropertiesError);
          } else if (teamPropertiesData && teamPropertiesData.length > 0) {
            // Get the property IDs from the team properties
            const propertyIds = teamPropertiesData.map((tp: { property_id: string }) => tp.property_id);
            console.log('[usePurchaseOrders] Accessible property IDs:', propertyIds);

            // Get purchase orders for these properties
            const { data: propertyOrders, error: propertyOrdersError } = await supabase
              .from('purchase_orders')
              .select(`
                *,
                property:property_id (name)
              `)
              .in('property_id', propertyIds)
              .neq('user_id', authState.user.id) // Exclude own orders
              .order('created_at', { ascending: false });

            if (propertyOrdersError) {
              console.error('[usePurchaseOrders] Error fetching property orders:', propertyOrdersError);
            } else if (propertyOrders && propertyOrders.length > 0) {
              teamOrders = propertyOrders;
              console.log(`[usePurchaseOrders] Successfully loaded ${teamOrders.length} team property orders`);

              // Log the first few orders for debugging
              if (propertyOrders.length > 0) {
                console.log('[usePurchaseOrders] Sample team property order:', propertyOrders[0]);
              }
            } else {
              console.log('[usePurchaseOrders] No purchase orders found for team properties');
            }

            // Also get orders directly associated with teams
            const { data: teamDirectOrders, error: teamDirectOrdersError } = await supabase
              .from('purchase_orders')
              .select(`
                *,
                property:property_id (name)
              `)
              .in('team_id', userTeams)
              .neq('user_id', authState.user.id) // Exclude own orders
              .order('created_at', { ascending: false });

            if (teamDirectOrdersError) {
              console.error('[usePurchaseOrders] Error fetching direct team orders:', teamDirectOrdersError);
            } else if (teamDirectOrders && teamDirectOrders.length > 0) {
              // Add these to the team orders
              teamOrders = [...teamOrders, ...teamDirectOrders];
              console.log(`[usePurchaseOrders] Added ${teamDirectOrders.length} direct team orders`);
            }
          }
        }

        // Step 3: Combine all orders and remove duplicates
        const allOrders = [...(ownOrders || []), ...teamOrders];
        console.log(`[usePurchaseOrders] Combined ${ownOrders?.length || 0} own orders with ${teamOrders.length} team orders`);

        // Filter out duplicate orders by ID
        const uniqueOrders = allOrders.reduce((acc, order) => {
          if (!acc.some((o: { id: string }) => o.id === order.id)) {
            acc.push(order);
          }
          return acc;
        }, [] as any[]);

        console.log(`[usePurchaseOrders] Filtered ${allOrders.length - uniqueOrders.length} duplicate orders`);

        if (!uniqueOrders || uniqueOrders.length === 0) {
          console.log('[usePurchaseOrders] No data returned after filtering');
          return [];
        }

        const data = uniqueOrders;

        console.log(`[usePurchaseOrders] Successfully loaded ${data.length} purchase orders`);

        // Format the data
        const formattedOrders = data.map(order => ({
          ...order,
          property_name: order.property?.name
        }));

        // Fetch items for each order
        const ordersWithItems = await Promise.all(
          formattedOrders.map(async (order) => {
            try {
              const { data: items, error: itemsError } = await supabase
                .from('purchase_order_items')
                .select('*')
                .eq('purchase_order_id', order.id);

              if (itemsError) {
                console.error('[usePurchaseOrders] Error fetching order items:', itemsError);
                return { ...order, items: [] };
              }

              return {
                ...order,
                items: items || []
              };
            } catch (err) {
              console.error('[usePurchaseOrders] Exception fetching items for order:', order.id, err);
              return { ...order, items: [] };
            }
          })
        );

        return ordersWithItems;
      } catch (err) {
        console.error('[usePurchaseOrders] Exception in queryFn:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: !!authState.user?.id // Only run the query if we have a user
  });

  // Fetch a single purchase order by ID
  const getPurchaseOrder = async (id: string) => {
    if (!authState.user?.id) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('purchase_orders')
      .select(`
        *,
        property:property_id (name)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching purchase order:', error);
      throw error;
    }

    // Fetch items for the order
    const { data: items, error: itemsError } = await supabase
      .from('purchase_order_items')
      .select('*')
      .eq('purchase_order_id', id);

    if (itemsError) {
      console.error('Error fetching order items:', itemsError);
      throw itemsError;
    }

    return {
      ...data,
      property_name: data.property?.name,
      items
    };
  };

  // Create a new purchase order with items
  const createPurchaseOrder = useMutation({
    mutationFn: async ({
      orderData,
      items
    }: {
      orderData: Omit<PurchaseOrder, 'id' | 'user_id' | 'created_at' | 'updated_at'>,
      items: Omit<PurchaseOrderItem, 'id' | 'purchase_order_id' | 'created_at'>[]
    }) => {
      console.log('[usePurchaseOrders] Creating purchase order with:', { orderData, items });

      // Make sure we have a user ID
      if (!authState.user?.id) {
        console.error('[usePurchaseOrders] No authenticated user');
        throw new Error('User not authenticated');
      }

      try {
        // Validate the property ID exists
        const { data: propertyCheck, error: propertyError } = await supabase
          .from('properties')
          .select('id')
          .eq('id', orderData.property_id)
          .single();

        if (propertyError || !propertyCheck) {
          console.error('[usePurchaseOrders] Property validation failed:', propertyError);
          throw new Error(`Invalid property ID: ${orderData.property_id}`);
        }

        // Add user_id to the order data
        const orderWithUserId = {
          ...orderData,
          user_id: authState.user.id
        };

        console.log('[usePurchaseOrders] Creating order with data:', orderWithUserId);

        // Insert the order first
        const { data: order, error: orderError } = await supabase
          .from('purchase_orders')
          .insert([orderWithUserId])
          .select()
          .single();

        if (orderError) {
          console.error('[usePurchaseOrders] Error creating purchase order:', orderError);
          throw orderError;
        }

        if (!order || !order.id) {
          console.error('[usePurchaseOrders] Order created but no ID returned');
          throw new Error('Failed to create purchase order: No order ID returned');
        }

        console.log('[usePurchaseOrders] Order created successfully with ID:', order.id);

        // Then insert all items
        if (items.length > 0) {
          // Format items to ensure they have all required fields
          const itemsWithOrderId = items.map(item => {
            // Ensure we have a valid item name
            const itemName = item.item_name || 'Unnamed Item';

            // Ensure we have a valid quantity
            const quantity = typeof item.quantity === 'number' ? item.quantity : 1;

            // Create a properly formatted item with all required fields
            return {
              purchase_order_id: order.id,
              inventory_item_id: item.inventory_item_id || null,
              item_name: itemName,
              quantity: quantity,
              price: typeof item.price === 'number' ? item.price : 0,
              amazon_url: item.amazon_url || '',
              walmart_url: item.walmart_url || ''
            };
          });

          console.log('[usePurchaseOrders] Adding items to order:', itemsWithOrderId);

          // Try to insert all items at once first for better performance
          const { error: bulkItemError } = await supabase
            .from('purchase_order_items')
            .insert(itemsWithOrderId);

          // If bulk insert fails, try one by one
          if (bulkItemError) {
            console.warn('[usePurchaseOrders] Bulk insert failed, trying one by one:', bulkItemError);

            let hasError = false;
            let successCount = 0;

            for (const item of itemsWithOrderId) {
              const { error: itemError } = await supabase
                .from('purchase_order_items')
                .insert([item]);

              if (itemError) {
                console.error('[usePurchaseOrders] Error adding order item:', itemError, item);
                hasError = true;
              } else {
                successCount++;
              }
            }

            if (hasError && successCount === 0) {
              console.error('[usePurchaseOrders] All items failed to add, deleting order');

              // Attempt to delete the order since all items failed
              await supabase.from('purchase_orders').delete().eq('id', order.id);

              throw new Error('Failed to add any items to purchase order');
            } else if (hasError) {
              console.warn(`[usePurchaseOrders] Some items failed, but ${successCount}/${itemsWithOrderId.length} succeeded`);
              // We'll continue since at least some items were added
            }
          }
        }

        console.log('[usePurchaseOrders] Purchase order creation completed successfully');
        return order;
      } catch (error) {
        console.error('[usePurchaseOrders] Exception in createPurchaseOrder:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('[usePurchaseOrders] Purchase order created successfully:', data);
      queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] });
      toast.success('Purchase order created successfully');
    },
    onError: (error) => {
      console.error('[usePurchaseOrders] Error creating purchase order:', error);
      toast.error(`Failed to create purchase order: ${error.message}`);
    }
  });

  // Update an order's status
  const updateOrderStatus = useMutation({
    mutationFn: async ({
      orderId,
      status
    }: {
      orderId: string,
      status: POStatus
    }) => {
      const { data, error } = await supabase
        .from('purchase_orders')
        .update({ status })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        console.error('[usePurchaseOrders] Error updating order status:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] });
      toast.success('Order status updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update order status: ${error.message}`);
    }
  });

  // Update an order item's quantity
  const updateOrderItemQuantity = useMutation({
    mutationFn: async ({
      itemId,
      quantity,
      orderId
    }: {
      itemId: string,
      quantity: number,
      orderId: string
    }) => {
      // Update the item
      const { data, error } = await supabase
        .from('purchase_order_items')
        .update({ quantity })
        .eq('id', itemId)
        .select()
        .single();

      if (error) {
        console.error('[usePurchaseOrders] Error updating item quantity:', error);
        throw error;
      }

      // Recalculate order total
      const { data: items, error: itemsError } = await supabase
        .from('purchase_order_items')
        .select('*')
        .eq('purchase_order_id', orderId);

      if (itemsError) {
        console.error('[usePurchaseOrders] Error fetching order items:', itemsError);
        throw itemsError;
      }

      const totalPrice = items.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0);

      // Update the order total
      const { error: updateOrderError } = await supabase
        .from('purchase_orders')
        .update({ total_price: totalPrice })
        .eq('id', orderId);

      if (updateOrderError) {
        console.error('[usePurchaseOrders] Error updating order total:', updateOrderError);
        throw updateOrderError;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] });
      toast.success('Item quantity updated');
    },
    onError: (error) => {
      toast.error(`Failed to update item quantity: ${error.message}`);
    }
  });

  // Delete a purchase order
  const deletePurchaseOrder = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('purchase_orders')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('[usePurchaseOrders] Error deleting purchase order:', error);
        throw error;
      }

      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchaseOrders'] });
      toast.success('Purchase order deleted successfully');
    },
    onError: (error) => {
      toast.error(`Failed to delete purchase order: ${error.message}`);
    }
  });

  // Add error retry effect
  useEffect(() => {
    // Only retry on actual errors, not on empty data
    if (isError) {
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[usePurchaseOrders] Auto-retrying data fetch due to error');
          retryFetch();
        } else {
          toast.error('Failed to load purchase orders after multiple attempts. Please try refreshing the page.');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, retryFetch]);

  return {
    purchaseOrders: purchaseOrders || [],
    isLoading,
    error,
    isError,
    getPurchaseOrder,
    createPurchaseOrder,
    updateOrderStatus,
    updateOrderItemQuantity,
    deletePurchaseOrder,
    retryFetch
  };
};
