import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { toast } from 'sonner';
import { retrySupabaseFetch } from '@/utils/cacheUtils';

export interface DamageReport {
  id: string;
  title: string;
  description: string;
  status: string;
  property_id: string;
  property_name?: string;
  created_at: string;
  updated_at: string;
  provider_id?: string;
  provider_name?: string;
  platform?: string;
}

export const useDamageReports = () => {
  const [damageReports, setDamageReports] = useState<DamageReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();
  const { userTeams } = usePermissions();
  const userId = authState.user?.id;

  // Add a loading ref to prevent race conditions
  const isLoadingRef = useRef(false);

  // We're not using this ref to prevent automatic data loading anymore
  const hasFetchedRef = useRef(false);

  // No longer tracking visibility state

  const fetchDamageReports = useCallback(async () => {
    // Check if we're already in the middle of a fetch operation using the ref
    if (isLoadingRef.current) {
      console.log('[useDamageReports] Already loading, skipping duplicate fetch');
      return;
    }

    // Set the loading ref to true
    isLoadingRef.current = true;

    if (!userId) {
      console.log('[useDamageReports] No user ID, skipping fetch');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('[useDamageReports] Fetching damage reports for user:', userId);

      // Check if the user is authenticated
      let sessionData: { session: any } | null = null;
      try {
        const { data, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) {
          console.error('[useDamageReports] Session error:', sessionError);
          // Don't throw, just set an empty array and return
          setDamageReports([]);
          setLoading(false);
          return;
        }
        sessionData = data;
      } catch (e) {
        console.error('[useDamageReports] Exception checking session:', e);
        // Don't throw, just set an empty array and return
        setDamageReports([]);
        setLoading(false);
        return;
      }

      if (!sessionData?.session) {
        console.log('[useDamageReports] No active session, skipping fetch');
        setDamageReports([]);
        setLoading(false);
        return;
      }

      // Use our super simple database function to get all damage reports with retry mechanism
      console.log('[useDamageReports] Using get_user_damage_reports_simple function with retry');

      const { data: reports, error: reportsError } = await retrySupabaseFetch(async () => {
        return await supabase.rpc('get_user_damage_reports_simple', { p_user_id: userId });
      });

      if (reportsError) {
        console.error('[useDamageReports] Error fetching reports with RPC:', reportsError);
        throw reportsError;
      }

      console.log(`[useDamageReports] Successfully loaded ${reports?.length || 0} reports with RPC`);

      // Sort by creation date, most recent first
      const sortedReports = [...(reports || [])].sort((a: { created_at: string }, b: { created_at: string }) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Format reports for the UI
      const formattedReports: DamageReport[] = sortedReports.map((report: any) => ({
        id: report.id,
        title: report.title,
        description: report.description,
        status: report.status,
        property_id: report.property_id,
        property_name: report.property_name,
        created_at: report.created_at,
        updated_at: report.updated_at,
        provider_id: report.provider_id,
        provider_name: report.provider_name,
        platform: report.platform
      }));

      setDamageReports(formattedReports);
    } catch (err: any) {
      console.error('[useDamageReports] Error fetching damage reports:', err);

      // Handle network errors specifically
      if (err.message && err.message.includes('Failed to fetch')) {
        console.log('[useDamageReports] Network error detected');
        setError('Network connection issue. Please check your internet connection.');
        // Don't show toast for network errors to avoid spamming the user
      } else {
        setError(err.message || 'An error occurred while loading damage reports');
        // Only show toast for non-network errors
        toast.error('Failed to load damage reports');
      }

      // Set empty array for graceful degradation
      setDamageReports([]);
    } finally {
      setLoading(false);
      // Reset the loading ref
      isLoadingRef.current = false;
      console.log('[useDamageReports] Fetch completed, loading state reset');
    }
  }, [userId, authState?.profile?.is_super_admin, authState?.profile?.role, userTeams]);

  // Use a single useEffect for all data loading
  useEffect(() => {
    // Only fetch if we have a userId
    if (!userId) {
      return;
    }

    // Fetch when userId, role, or team membership changes
    console.log('[useDamageReports] User ID, role, or team membership changed, fetching data...');

    // Mark that we're going to fetch data
    hasFetchedRef.current = true;

    // Add a small delay to avoid race conditions
    const timer = setTimeout(() => {
      fetchDamageReports();
    }, 100);

    return () => clearTimeout(timer);
  }, [userId, authState?.profile?.role, userTeams, fetchDamageReports]); // Include fetchDamageReports in dependencies

  // We're now relying on React Query's built-in refetchOnWindowFocus functionality
  // instead of custom visibility change handlers
  useEffect(() => {
    console.log('[useDamageReports] Using React Query\'s built-in refetchOnWindowFocus for data refreshing');

    // Set up a timer to periodically check for data
    const dataCheckTimer = setInterval(() => {
      if (userId && damageReports.length === 0) {
        console.log('[useDamageReports] No damage reports loaded, checking data');
        fetchDamageReports();
      }
    }, 60000); // Check every minute

    return () => {
      clearInterval(dataCheckTimer);
    };
  }, [userId, fetchDamageReports, damageReports.length]);

  const addDamageReport = async (report: Omit<DamageReport, 'id' | 'created_at' | 'updated_at'>) => {
    if (!userId) return false;

    try {
      console.log('[useDamageReports] Adding damage report:', report);

      const { data, error } = await supabase
        .from('damage_reports')
        .insert({
          ...report,
          user_id: userId
        })
        .select();

      if (error) {
        console.error('[useDamageReports] Error adding damage report:', error);
        throw error;
      }

      if (data && data.length > 0) {
        const newReport: DamageReport = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description,
          status: data[0].status,
          property_id: data[0].property_id,
          created_at: data[0].created_at,
          updated_at: data[0].updated_at,
          provider_id: data[0].provider_id,
          platform: data[0].platform
        };

        setDamageReports(prev => [newReport, ...prev]);
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('[useDamageReports] Error adding damage report:', error);
      toast.error(`Failed to add damage report: ${error.message}`);
      return false;
    }
  };

  const updateDamageReport = async (id: string, report: Partial<DamageReport>) => {
    if (!userId) return false;

    try {
      console.log('[useDamageReports] Updating damage report:', id, report);

      const { error } = await supabase
        .from('damage_reports')
        .update(report)
        .eq('id', id);

      if (error) {
        console.error('[useDamageReports] Error updating damage report:', error);
        throw error;
      }

      setDamageReports(prev =>
        prev.map(r => r.id === id ? { ...r, ...report } : r)
      );

      return true;
    } catch (error: any) {
      console.error('[useDamageReports] Error updating damage report:', error);
      toast.error(`Failed to update damage report: ${error.message}`);
      return false;
    }
  };

  const deleteDamageReport = async (id: string) => {
    if (!userId) return false;

    try {
      console.log('[useDamageReports] Deleting damage report:', id);

      const { error } = await supabase
        .from('damage_reports')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('[useDamageReports] Error deleting damage report:', error);
        throw error;
      }

      setDamageReports(prev => prev.filter(r => r.id !== id));
      return true;
    } catch (error: any) {
      console.error('[useDamageReports] Error deleting damage report:', error);
      toast.error(`Failed to delete damage report: ${error.message}`);
      return false;
    }
  };

  return {
    damageReports,
    loading,
    error,
    fetchDamageReports,
    addDamageReport,
    updateDamageReport,
    deleteDamageReport
  };
};
