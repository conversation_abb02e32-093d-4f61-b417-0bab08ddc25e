import { useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

// Map routes to their relevant query keys
const routeQueryKeyMap: Record<string, string[]> = {
  '/dashboard': ['properties', 'maintenanceTasks', 'maintenanceTasksV2', 'inventoryItems', 'damageReports', 'purchaseOrders'],
  '/properties': ['properties'],
  '/maintenance': ['maintenanceTasks', 'maintenanceTasksV2', 'properties'],
  '/inventory': ['inventoryItems', 'properties'],
  '/damages': ['damageReports', 'damageReportsV2', 'properties'],
  '/purchase-orders': ['purchaseOrders', 'properties', 'inventoryItems'],
  '/teams': ['teamsV2', 'teamMembersV2', 'teamPropertiesV2', 'teamInvitationsV2', 'teams', 'teamMembers', 'teamProperties', 'teamInvitations'],
  '/collections': ['collections', 'inventoryItems', 'properties'],
  '/operations': ['properties', 'maintenanceTasks', 'maintenanceTasksV2', 'bookings', 'damageReports'],
  '/task-automation': ['automationRulesV2', 'maintenanceTasksV2', 'maintenanceTasks'],
  '/settings': ['userSettings', 'appearanceSettingsV2', 'notificationSettings', 'accountSettings'],
  '/settings/appearance': ['appearanceSettingsV2', 'userSettings'],
  '/settings/notifications': ['notificationSettings', 'userSettings'],
  '/settings/account': ['accountSettings', 'userSettings'],
  // Add other routes as needed
};

// Cache to prevent duplicate refreshes
const refreshCache = new Map<string, number>();
const REFRESH_COOLDOWN = 5000; // 5 seconds cooldown between refreshes for the same route

// Routes that are currently having issues and should be refreshed less frequently or not at all
const REFRESH_BLACKLIST = new Set<string>([]); // Previously included '/teams' but it's now fixed

/**
 * A hook that provides a function to refresh data when navigating to a new route
 */
export const useNavigationRefresh = () => {
  const queryClient = useQueryClient();
  const isRefreshingRef = useRef(false);
  const lastRefreshTimeRef = useRef<Record<string, number>>({});

  /**
   * Force refresh data for a specific route
   */
  const refreshRouteData = useCallback((route: string) => {
    // Prevent refreshing if already in progress
    if (isRefreshingRef.current) {
      console.log('[useNavigationRefresh] Already refreshing, skipping duplicate refresh');
      return;
    }

    // Handle both hash and non-hash routes
    // First, normalize the route by removing any leading or trailing slashes and hash
    let cleanRoute = route;

    // Handle hash router format (e.g., '/#/properties' or '#/properties')
    if (cleanRoute.includes('#/')) {
      // Extract the part after '#/'
      cleanRoute = cleanRoute.split('#/')[1] || '';
    } else if (cleanRoute.startsWith('#')) {
      // Handle case where it's just '#/properties'
      cleanRoute = cleanRoute.substring(1);
    }

    // Ensure the route starts with a slash
    if (!cleanRoute.startsWith('/')) {
      cleanRoute = '/' + cleanRoute;
    }

    // Remove trailing slash if present
    if (cleanRoute.length > 1 && cleanRoute.endsWith('/')) {
      cleanRoute = cleanRoute.slice(0, -1);
    }

    // Handle nested routes by extracting the base route
    // For example: '/properties/123' -> '/properties'
    // Special cases for specific routes
    let baseRoute: string;

    // Special case for maintenance/automation
    if (cleanRoute.startsWith('/maintenance/automation')) {
      baseRoute = '/task-automation';
      console.log(`[useNavigationRefresh] Special case: mapping /maintenance/automation to ${baseRoute}`);
    }
    // Special case for teams with IDs
    else if (cleanRoute.match(/^\/teams\/[a-zA-Z0-9-]+$/)) {
      baseRoute = '/teams';
      console.log(`[useNavigationRefresh] Special case: mapping ${cleanRoute} to ${baseRoute}`);
    }
    // Special case for properties with IDs
    else if (cleanRoute.match(/^\/properties\/[a-zA-Z0-9-]+$/)) {
      baseRoute = '/properties';
      console.log(`[useNavigationRefresh] Special case: mapping ${cleanRoute} to ${baseRoute}`);
    }
    // Special case for damage reports with IDs
    else if (cleanRoute.match(/^\/damages\/[a-zA-Z0-9-]+$/)) {
      baseRoute = '/damages';
      console.log(`[useNavigationRefresh] Special case: mapping ${cleanRoute} to ${baseRoute}`);
    }
    // Special case for settings routes
    else if (cleanRoute.startsWith('/settings/')) {
      // Map specific settings routes to their base routes
      if (cleanRoute.startsWith('/settings/appearance')) {
        baseRoute = '/settings/appearance';
      } else if (cleanRoute.startsWith('/settings/notifications')) {
        baseRoute = '/settings/notifications';
      } else if (cleanRoute.startsWith('/settings/account')) {
        baseRoute = '/settings/account';
      } else {
        baseRoute = '/settings';
      }
      console.log(`[useNavigationRefresh] Special case: mapping ${cleanRoute} to ${baseRoute}`);
    }
    // Default case - extract the first segment
    else {
      const segments = cleanRoute.split('/').filter(Boolean);
      baseRoute = segments.length > 0 ? `/${segments[0]}` : '/';
    }

    console.log(`[useNavigationRefresh] Original route: ${route}, Cleaned route: ${cleanRoute}, Base route: ${baseRoute}`);

    // Check if we've refreshed this route recently
    const now = Date.now();
    const lastRefreshTime = lastRefreshTimeRef.current[baseRoute] || 0;

    // Skip routes that are known to cause issues
    if (REFRESH_BLACKLIST.has(baseRoute)) {
      console.log(`[useNavigationRefresh] Route ${baseRoute} is blacklisted, skipping refresh entirely`);
      return;
    }

    // Use a longer cooldown for problematic routes
    const cooldownTime = REFRESH_COOLDOWN;

    if (now - lastRefreshTime < cooldownTime) {
      console.log(`[useNavigationRefresh] Route ${baseRoute} was refreshed recently, skipping`);
      return;
    }

    // Update the last refresh time
    lastRefreshTimeRef.current[baseRoute] = now;

    // Set refreshing flag
    isRefreshingRef.current = true;

    try {
      // Get query keys for this route
      const queryKeys = routeQueryKeyMap[baseRoute] || [];

      if (queryKeys.length > 0) {
        console.log(`[useNavigationRefresh] Invalidating query keys for ${baseRoute}: ${queryKeys.join(', ')}`);

        // Invalidate each query key with more aggressive options
        const invalidationPromises = queryKeys.map(async (key) => {
          console.log(`[useNavigationRefresh] Invalidating query key: ${key}`);

          try {
            // First invalidate the query
            await queryClient.invalidateQueries({
              queryKey: [key],
              refetchType: 'all', // Refetch all queries, not just active ones
              exact: false
            });

            // Then explicitly refetch to ensure data is loaded
            await queryClient.refetchQueries({
              queryKey: [key],
              exact: false,
              type: 'all'
            });

            console.log(`[useNavigationRefresh] Successfully invalidated and refetched: ${key}`);
          } catch (error) {
            console.error(`[useNavigationRefresh] Error invalidating/refetching ${key}:`, error);
          }
        });

        // Wait for all invalidations to complete
        Promise.all(invalidationPromises).then(() => {
          console.log(`[useNavigationRefresh] All query invalidations completed for ${baseRoute}`);
        }).catch(error => {
          console.error(`[useNavigationRefresh] Error in query invalidation batch:`, error);
        });

        // Only dispatch the event if we actually invalidated queries
        // This prevents infinite loops of events triggering more events
        const shouldDispatchEvent = !refreshCache.has(baseRoute) ||
                                   (now - (refreshCache.get(baseRoute) || 0) > REFRESH_COOLDOWN);

        if (shouldDispatchEvent) {
          // Update the cache
          refreshCache.set(baseRoute, now);

          // Dispatch a custom event to notify that data has been refreshed
          // But only if we haven't dispatched one recently for this route
          window.dispatchEvent(new CustomEvent('stayfu-navigation-refresh', {
            detail: {
              route: baseRoute,
              timestamp: now
            }
          }));

          console.log(`[useNavigationRefresh] Data refresh triggered for ${baseRoute}`);
        } else {
          console.log(`[useNavigationRefresh] Skipping event dispatch for ${baseRoute} to prevent loops`);
        }
      } else {
        console.log(`[useNavigationRefresh] No query keys found for route: ${baseRoute}`);
      }
    } finally {
      // Clear the refreshing flag after a short delay
      // This prevents rapid successive calls
      setTimeout(() => {
        isRefreshingRef.current = false;
      }, 500);
    }
  }, [queryClient]);

  return { refreshRouteData };
};
