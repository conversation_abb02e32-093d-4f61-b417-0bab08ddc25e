import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { format } from 'date-fns';
import { mapPropertyToCardProperty } from '@/utils/propertyUtils';
// Use the RPC version of the hook for better performance
import { useMaintenanceTasksRPC } from './useMaintenanceTasksRPC';
import { usePurchaseOrders } from './usePurchaseOrders';
import { useInventoryQueryV2 } from './useInventoryQueryV2';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { toast } from 'sonner';
// Removed unused imports

interface DamageReport {
  id: string;
  title: string;
  property_name?: string;
  status: string;
  created_at: string;
  properties?: {
    name?: string;
  }
}

export const useDashboardData = (forceSlow = false) => {
  const { authState } = useAuth();
  const { isAdmin } = usePermissions();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const userId = authState?.user?.id;

  // Initialize team state
  const [userTeams, setUserTeams] = useState<string[]>([]);
  const [accessibleTeams, setAccessibleTeams] = useState<string[]>([]);
  const [damageReports, setDamageReports] = useState<DamageReport[]>([]);

  // Team related data
  useEffect(() => {
    if (!userId) {
      console.log('[useDashboardData] No authenticated user, skipping team fetch');
      return;
    }

    const fetchTeams = async () => {
      try {
        console.log('[useDashboardData] Fetching user teams');

        // Get user teams (teams the user is a member of)
        const { data: teamMemberships, error: teamError } = await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', userId);

        if (teamError) {
          console.error('[useDashboardData] Error fetching teams:', teamError);
          return;
        }

        if (teamMemberships && teamMemberships.length > 0) {
          const teams = teamMemberships.map((tm: { team_id: string }) => tm.team_id);
          console.log('[useDashboardData] User is a member of teams:', teams);
          setUserTeams(teams);

          // Set accessible teams based on role
          if (isAdmin()) {
            setAccessibleTeams(['*']); // Admins can access all teams
          } else {
            setAccessibleTeams(teams);
          }
        } else {
          console.log('[useDashboardData] User is not a member of any teams');
          setUserTeams([]);
          setAccessibleTeams([]);
        }
      } catch (err) {
        console.error('[useDashboardData] Error in fetchTeams:', err);
      }
    };

    fetchTeams();
  }, [userId]);

  // Get team property IDs
  const { properties = [], loading: propertiesLoading } = usePropertiesQueryV2();

  // Damage Reports
  useEffect(() => {
    if (!userId) {
      console.log('[useDashboardData] No authenticated user, skipping damage reports fetch');
      return;
    }

    if (accessibleTeams.length === 0 && !isAdmin()) {
      console.log('[useDashboardData] User has no teams and is not admin, showing empty damage reports');
      setDamageReports([]);
      return;
    }

    const fetchDamageReports = async () => {
      try {
        console.log('[useDashboardData] Fetching damage reports');
        console.log('[useDashboardData] User is admin:', isAdmin());
        console.log('[useDashboardData] User teams:', userTeams);
        console.log('[useDashboardData] Accessible teams:', accessibleTeams);

        let query = supabase
          .from('damage_reports')
          .select('*, properties(name)')
          .order('created_at', { ascending: false })
          .limit(5);

        // Apply different filters based on user role
        if (isAdmin()) {
          // Admins see all damage reports
          console.log('[useDashboardData] Admin fetching all damage reports');
        } else if (accessibleTeams.includes('*')) {
          // Special case for wildcard access
          console.log('[useDashboardData] User has wildcard access to teams');
        } else if (accessibleTeams.length > 0) {
          // For team members, we need to get team property IDs first
          console.log('[useDashboardData] Fetching team property damage reports');

          // We'll handle this in the async chain below
          // For now, just set a flag to indicate we need to do this
          query = query.eq('user_id', userId); // Default filter, will be replaced
        } else {
          // Default case - just show user's own reports
          console.log('[useDashboardData] Showing only user\'s own reports');
          query = query.eq('user_id', userId);
        }

        // Special handling for team members
        if (!isAdmin() && accessibleTeams.length > 0 && !accessibleTeams.includes('*')) {
          // First get team properties
          supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', accessibleTeams)
            .then(({ data: teamPropertiesData, error: teamPropsError }) => {
              if (teamPropsError) {
                console.error('[useDashboardData] Error fetching team properties:', teamPropsError);
                // Fall back to user's own reports
                return supabase
                  .from('damage_reports')
                  .select('*, properties(name)')
                  .eq('user_id', userId)
                  .order('created_at', { ascending: false })
                  .limit(5);
              }

              if (teamPropertiesData && teamPropertiesData.length > 0) {
                // Get the property IDs from the team properties
                const propertyIds = teamPropertiesData.map((tp: { property_id: string }) => tp.property_id);
                console.log('[useDashboardData] Team property IDs:', propertyIds);

                // Query damage reports for these properties
                return supabase
                  .from('damage_reports')
                  .select('*, properties(name)')
                  .in('property_id', propertyIds)
                  .order('created_at', { ascending: false })
                  .limit(5);
              } else {
                console.log('[useDashboardData] No team properties found, falling back to user reports');
                // Fall back to user's own reports
                return supabase
                  .from('damage_reports')
                  .select('*, properties(name)')
                  .eq('user_id', userId)
                  .order('created_at', { ascending: false })
                  .limit(5);
              }
            })
            .then(({ data, error }) => {
              if (error) {
                console.error('[useDashboardData] Error fetching damage reports:', error);
                setDamageReports([]);
                return;
              }

              if (data) {
                console.log(`[useDashboardData] Loaded ${data.length} damage reports`);
                setDamageReports(data.map(item => ({
                  ...item,
                  property_name: item.properties?.name
                })));
              }
            });
        } else {
          // For admins, wildcard access, or users with no teams
          query.then(({ data, error }) => {
            if (error) {
              console.error('[useDashboardData] Error fetching damage reports:', error);
              setDamageReports([]);
              return;
            }

            if (data) {
              console.log(`[useDashboardData] Loaded ${data.length} damage reports`);
              setDamageReports(data.map(item => ({
                ...item,
                property_name: item.properties?.name
              })));
            }
          });
        }
      } catch (err) {
        console.error('[useDashboardData] Error in fetchDamageReports:', err);
        setDamageReports([]);
      }
    };

    fetchDamageReports();
  }, [userId, userTeams, accessibleTeams]);

  // Get maintenance tasks
  const maintenanceTaskResult = useMaintenanceTasksRPC({
    mode: 'dashboard'
  });

  // Get purchase orders
  const { data: purchaseOrders = [], isLoading: purchaseOrdersLoading } = usePurchaseOrders({
    limit: 5
  });

  // Get inventory
  const {
    inventoryItems = [],
    loading: inventoryLoading
  } = useInventoryQueryV2();

  const fetchDashboardData = useCallback(async () => {
    if (!userId) {
      console.log('[useDashboardData] No authenticated user, skipping dashboard fetch');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('[useDashboardData] Fetching dashboard data');
      console.log('[useDashboardData] User is admin:', isAdmin());
      console.log('[useDashboardData] User teams:', userTeams);
      console.log('[useDashboardData] Accessible teams:', accessibleTeams);

      let query = supabase
        .from('damage_reports')
        .select('*, properties(name)')
        .order('created_at', { ascending: false })
        .limit(5);

      // Apply different filters based on user role
      if (isAdmin()) {
        // Admins see all damage reports
        console.log('[useDashboardData] Admin fetching all damage reports');
      } else if (accessibleTeams.includes('*')) {
        // Special case for wildcard access
        console.log('[useDashboardData] User has wildcard access to teams');
      } else if (accessibleTeams.length > 0) {
        // For team members, we need to get team property IDs first
        console.log('[useDashboardData] Fetching team property damage reports');

        // We'll handle this in the async chain below
        // For now, just set a flag to indicate we need to do this
        query = query.eq('user_id', userId); // Default filter, will be replaced
      } else {
        // Default case - just show user's own reports
        console.log('[useDashboardData] Showing only user\'s own reports');
        query = query.eq('user_id', userId);
      }

      // Special handling for team members
      if (!isAdmin() && accessibleTeams.length > 0 && !accessibleTeams.includes('*')) {
        // First get team properties
        supabase
          .from('team_properties')
          .select('property_id')
          .in('team_id', accessibleTeams)
          .then(({ data: teamPropertiesData, error: teamPropsError }) => {
            if (teamPropsError) {
              console.error('[useDashboardData] Error fetching team properties:', teamPropsError);
              // Fall back to user's own reports
              return supabase
                .from('damage_reports')
                .select('*, properties(name)')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(5);
            }

            if (teamPropertiesData && teamPropertiesData.length > 0) {
              // Get the property IDs from the team properties
              const propertyIds = teamPropertiesData.map((tp: { property_id: string }) => tp.property_id);
              console.log('[useDashboardData] Team property IDs:', propertyIds);

              // Query damage reports for these properties
              return supabase
                .from('damage_reports')
                .select('*, properties(name)')
                .in('property_id', propertyIds)
                .order('created_at', { ascending: false })
                .limit(5);
            } else {
              console.log('[useDashboardData] No team properties found, falling back to user reports');
              // Fall back to user's own reports
              return supabase
                .from('damage_reports')
                .select('*, properties(name)')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(5);
            }
          })
          .then(({ data, error }) => {
            if (error) {
              console.error('[useDashboardData] Error fetching damage reports:', error);
              setDamageReports([]);
              return;
            }

            if (data) {
              console.log(`[useDashboardData] Loaded ${data.length} damage reports`);
              setDamageReports(data.map(item => ({
                ...item,
                property_name: item.properties?.name
              })));
            }
          });
      } else {
        // For admins, wildcard access, or users with no teams
        query.then(({ data, error }) => {
          if (error) {
            console.error('[useDashboardData] Error fetching damage reports:', error);
            setDamageReports([]);
            return;
          }

          if (data) {
            console.log(`[useDashboardData] Loaded ${data.length} damage reports`);
            setDamageReports(data.map(item => ({
              ...item,
              property_name: item.properties?.name
            })));
          }
        });
      }

      setLoading(false);
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'Unknown error';
      console.error('[useDashboardData] Error:', errorMessage, e);
      setError(e instanceof Error ? e : new Error('Unknown error occurred'));
      setLoading(false);
    }
  }, [userId, userTeams, accessibleTeams, maintenanceTaskResult.refreshTasks, properties.length, maintenanceTaskResult.tasks.length]);

  // Add a simulated delay for testing if forceSlow is true
  useEffect(() => {
    if (forceSlow) {
      const timer = setTimeout(() => {
        fetchDashboardData();
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      fetchDashboardData();
    }
  }, [fetchDashboardData, forceSlow]);

  // Calculate property stats
  const propertyStats = useMemo(() => {
    if (!properties.length) return null;

    const occupied = properties.filter(p => p.is_occupied).length;
    const occupancyRate = properties.length > 0 ? Math.round((occupied / properties.length) * 100) : 0;

    return {
      total: properties.length,
      occupied,
      vacant: properties.length - occupied,
      occupancyRate
    };
  }, [properties]);

  // Calculate maintenance stats
  const maintenanceStats = useMemo(() => {
    if (!maintenanceTaskResult.tasks || maintenanceTaskResult.tasks.length === 0) {
      return {
        total: 0,
        pending: 0,
        inProgress: 0,
        completed: 0,
        recentlyCompleted: []
      };
    }

    const tasks = maintenanceTaskResult.tasks;

    const pendingTasks = tasks.filter(task => task.status === 'pending').length;
    const inProgressTasks = tasks.filter(task => ['assigned', 'in_progress'].includes(task.status)).length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;

    // Get 5 most recently completed tasks
    const recentlyCompletedTasks = tasks
      .filter(task => task.status === 'completed')
      .sort((a, b) => {
        const dateA = a.completed_at ? new Date(a.completed_at).getTime() : 0;
        const dateB = b.completed_at ? new Date(b.completed_at).getTime() : 0;
        return dateB - dateA;
      })
      .slice(0, 5)
      .map(task => ({
        id: task.id,
        title: task.title,
        completed_at: task.completed_at ? format(new Date(task.completed_at), 'MMM d, yyyy') : '',
        property_name: task.property_name || 'Unknown Property'
      }));

    return {
      total: tasks.length,
      pending: pendingTasks,
      inProgress: inProgressTasks,
      completed: completedTasks,
      recentlyCompleted: recentlyCompletedTasks
    };
  }, [maintenanceTaskResult.tasks]);

  // Calculate purchase order stats
  const purchaseOrderStats = useMemo(() => {
    if (!purchaseOrders || purchaseOrders.length === 0) {
      return {
        total: 0,
        pending: 0,
        ordered: 0,
        delivered: 0,
        recent: []
      };
    }

    const pendingOrders = purchaseOrders.filter(order => order.status === 'pending').length;
    const orderedOrders = purchaseOrders.filter(order => order.status === 'ordered').length;
    const deliveredOrders = purchaseOrders.filter(order => order.status === 'delivered').length;

    // Get 5 most recent orders
    const recentOrders = [...purchaseOrders]
      .sort((a, b) => {
        const dateA = new Date(a.created_at || '').getTime();
        const dateB = new Date(b.created_at || '').getTime();
        return dateB - dateA;
      })
      .slice(0, 5)
      .map(order => ({
        id: order.id,
        title: order.title || 'Unnamed Order',
        status: order.status,
        created_at: order.created_at ? format(new Date(order.created_at), 'MMM d, yyyy') : '',
        property_name: order.property?.name || 'Unknown Property'
      }));

    return {
      total: purchaseOrders.length,
      pending: pendingOrders,
      ordered: orderedOrders,
      delivered: deliveredOrders,
      recent: recentOrders
    };
  }, [purchaseOrders]);

  // Calculate inventory stats
  const inventoryStats = useMemo(() => {
    if (!inventoryItems || inventoryItems.length === 0) {
      return {
        total: 0,
        lowStock: 0,
        outOfStock: 0,
        value: 0
      };
    }

    let totalValue = 0;
    let lowStockCount = 0;
    let outOfStockCount = 0;

    inventoryItems.forEach(item => {
      // Calculate value
      const quantity = item.quantity || 0;
      const price = item.price || 0;
      const itemValue = quantity * price;
      if (!isNaN(itemValue)) {
        totalValue += itemValue;
      }

      // Check stock levels
      if (quantity === 0) {
        outOfStockCount++;
      } else if (item.minQuantity && quantity <= item.minQuantity) {
        lowStockCount++;
      }
    });

    return {
      total: inventoryItems.length,
      lowStock: lowStockCount,
      outOfStock: outOfStockCount,
      value: totalValue
    };
  }, [inventoryItems]);

  // Return everything needed for the dashboard
  return {
    loading: loading || propertiesLoading || maintenanceTaskResult.loading || purchaseOrdersLoading || inventoryLoading,
    error,
    refetch: fetchDashboardData,
    maintenanceTasks: maintenanceTaskResult.tasks,
    properties: properties.map(p => mapPropertyToCardProperty(p)),
    propertyStats,
    maintenanceStats,
    damageReports,
    purchaseOrders,
    purchaseOrderStats,
    inventoryItems,
    inventoryStats,
    isRefreshing: false, // Removed isRefetching as it's not available in the new hook
    refreshTasks: maintenanceTaskResult.refreshTasks
  };
};
