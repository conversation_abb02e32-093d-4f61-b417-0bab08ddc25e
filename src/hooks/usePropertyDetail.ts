import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Property } from '@/hooks/useProperties';
import { CollectionWithBudget } from '@/components/properties/PropertyCard';
import { toast } from 'sonner';

interface UsePropertyDetailReturn {
  property: Property | null;
  loading: boolean;
  error: string | null;
  fetchProperty: () => Promise<void>;
  setProperty: React.Dispatch<React.SetStateAction<Property | null>>;
}

const usePropertyDetail = (id: string | undefined, userId: string | undefined): UsePropertyDetailReturn => {
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProperty();
  }, [id, userId]);

  const fetchProperty = async () => {
    if (!id || !userId) return;

    // Add a timeout to prevent infinite loading
    const timeoutPromise = new Promise<null>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Property loading timed out. Please try refreshing the page.'));
      }, 15000); // 15 seconds timeout
    });

    try {
      setLoading(true);
      setError(null);

      console.log(`[usePropertyDetail] Fetching property ${id} for user ${userId}`);

      // Try to use our simple function first
      let propertyData: Property | null = null;
      try {
        // Use Promise.race to implement timeout
        const fetchPromise = supabase.rpc(
          'get_property_details_simple',
          { p_property_id: id }
        );

        const result = await Promise.race([fetchPromise, timeoutPromise]);
        const { data: rpcData, error: rpcError } = result as any;

        if (!rpcError && rpcData && rpcData.length > 0) {
          console.log(`[usePropertyDetail] Property found via simple function: ${rpcData[0].name}`);
          propertyData = rpcData[0];
        } else {
          console.error('[usePropertyDetail] Simple function error:', rpcError);
        }
      } catch (rpcError) {
        console.error('[usePropertyDetail] Simple function error:', rpcError);
        // If it's a timeout error, throw it to be caught by the outer catch
        if (rpcError instanceof Error && rpcError.message.includes('timed out')) {
          throw rpcError;
        }
      }

      // If that fails, try direct query
      if (!propertyData) {
        console.log('[usePropertyDetail] Simple function failed, trying direct query');

        try {
          // Use Promise.race to implement timeout for direct query too
          const directQueryPromise = supabase
            .from('properties')
            .select('*')
            .eq('id', id)
            .single();

          const directResult = await Promise.race([directQueryPromise, timeoutPromise]);
          const { data, error } = directResult as any;

          if (error) {
            console.error('[usePropertyDetail] Error fetching property:', error);
            throw error;
          }

          if (!data) {
            console.error('[usePropertyDetail] Property not found');
            throw new Error('Property not found');
          }

          propertyData = data;
        } catch (directError) {
          console.error('[usePropertyDetail] Error in direct query:', directError);
          // If it's a timeout error, provide a user-friendly message
          if (directError instanceof Error && directError.message.includes('timed out')) {
            throw new Error('Property loading timed out. Please try refreshing the page.');
          }
          throw directError;
        }
      }

      if (propertyData) {
        console.log(`[usePropertyDetail] Property found: ${propertyData.name}`);

        // Process the property data
        let formattedCollections: CollectionWithBudget[] = [];

        if (propertyData.collections) {
          if (Array.isArray(propertyData.collections)) {
            formattedCollections = propertyData.collections
              .map((col): CollectionWithBudget | null => {
                if (typeof col === 'string' && (col as string).trim()) {
                  return { name: (col as string).trim() };
                } else if (col && typeof col === 'object' && 'name' in col && typeof col.name === 'string') {
                  // Assuming if it's an object with a name property, it's a CollectionWithBudget or similar
                  return col as CollectionWithBudget;
                }
                return null;
              })
              .filter((item): item is CollectionWithBudget => item !== null);
          } else if (typeof propertyData.collections === 'object' && propertyData.collections !== null) {
            Object.entries(propertyData.collections)
              .forEach(([key, value]) => {
                if (!key || key === '') return;

                if (typeof value === 'object' && value !== null) {
                  formattedCollections.push({
                    name: key,
                    ...(value as any)
                  });
                } else {
                  formattedCollections.push({ name: key });
                }
              });
          }
        }

        setProperty({
          id: propertyData.id,
          name: propertyData.name,
          address: propertyData.address,
          city: propertyData.city,
          state: propertyData.state,
          zip: propertyData.zip,
          image_url: propertyData.image_url,
          bedrooms: propertyData.bedrooms || 1,
          bathrooms: propertyData.bathrooms || 1,
          budget: propertyData.budget || 0,
          ical_url: propertyData.ical_url || '',
          next_booking: propertyData.next_booking || '',
          next_checkin_date: propertyData.next_checkin_date || '',
          next_checkin_formatted: propertyData.next_checkin_formatted || '',
          collections: formattedCollections,
          last_ical_sync: propertyData.last_ical_sync || '',
          is_occupied: propertyData.is_occupied,
          current_checkout: propertyData.current_checkout,
          timezone: propertyData.timezone || 'America/Los_Angeles',
          check_in_time: propertyData.check_in_time || '15:00:00',
          check_out_time: propertyData.check_out_time || '11:00:00'
        });
      } else {
        console.warn('[usePropertyDetail] Property data is null or undefined.');
        setProperty(null); // Ensure property state is null if data is null
      }
    } catch (error: any) {
      console.error('Error fetching property:', error);

      // Check if it's an authentication error
      if (error.message?.includes('JWT') ||
          error.message?.includes('auth') ||
          error.message?.includes('session') ||
          error.message?.includes('token')) {
        console.log('[usePropertyDetail] Authentication error detected, attempting to refresh session');

        try {
          // Try to refresh the session
          const { data } = await supabase.auth.getSession();
          if (data.session) {
            console.log('[usePropertyDetail] Session refreshed, retrying property fetch');
            // Wait a moment before retrying
            setTimeout(() => {
              fetchProperty();
            }, 1000);
            return;
          }
        } catch (refreshError) {
          console.error('[usePropertyDetail] Failed to refresh session:', refreshError);
        }
      }

      // Set a user-friendly error message
      const errorMessage = error.message?.includes('timed out')
        ? 'Property loading timed out. Please try refreshing the page.'
        : `Failed to load property: ${error.message}`;

      setError(errorMessage);
      toast.error('Failed to load property details', {
        description: 'Please try refreshing the page',
        action: {
          label: 'Retry',
          onClick: () => fetchProperty()
        }
      });
    } finally {
      setLoading(false);
    }
  };

  return { property, loading, error, fetchProperty, setProperty };
};

export default usePropertyDetail;
