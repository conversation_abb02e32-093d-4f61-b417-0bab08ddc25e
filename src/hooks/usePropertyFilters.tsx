
import { useState, useMemo } from 'react';
import { Property } from './useProperties';

export interface FilterState {
  searchTerm?: string;
  city?: string;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
}

export const usePropertyFilters = (properties: Property[]) => {
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: '',
    city: '',
    minBedrooms: 0,
    maxBedrooms: 10,
    minBathrooms: 0,
    maxBathrooms: 10
  });

  const filteredProperties = useMemo(() => {
    return properties.filter(property => {
      // Filter by search term
      if (filters.searchTerm && !property.name.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
        return false;
      }

      // Filter by city
      if (filters.city && filters.city !== 'all_cities' && property.city !== filters.city) {
        return false;
      }

      // Filter by bedrooms
      if (
        (filters.minBedrooms !== undefined && property.bedrooms < filters.minBedrooms) ||
        (filters.maxBedrooms !== undefined && property.bedrooms > filters.maxBedrooms)
      ) {
        return false;
      }

      // Filter by bathrooms
      if (
        (filters.minBathrooms !== undefined && property.bathrooms < filters.minBathrooms) ||
        (filters.maxBathrooms !== undefined && property.bathrooms > filters.maxBathrooms)
      ) {
        return false;
      }

      return true;
    });
  }, [properties, filters]);

  return {
    filters,
    setFilters,
    filteredProperties
  };
};
