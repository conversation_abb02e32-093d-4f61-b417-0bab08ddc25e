import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { UserSettingKey } from '@/types/settings';

export interface AppearanceSettings {
  dark_mode: boolean;
  compact_mode: boolean;
  animations: boolean;
}

export interface AppearanceSettingsData {
  settings: AppearanceSettings;
  loading: boolean;
  error: string | null;
  isError: boolean;
  updateSetting: (key: UserSettingKey, value: boolean) => Promise<boolean>;
  refreshSettings: () => Promise<void>;
}

const DEFAULT_SETTINGS: AppearanceSettings = {
  dark_mode: false,
  compact_mode: false,
  animations: true
};

/**
 * A hook for fetching and updating appearance settings using React Query
 */
export const useAppearanceSettingsQuery = (): AppearanceSettingsData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);

  // Function to retry data fetching manually
  const refreshSettings = useCallback(async () => {
    console.log('[useAppearanceSettingsQuery] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['appearanceSettings'] });
    await queryClient.refetchQueries({ queryKey: ['appearanceSettings'] });
  }, [queryClient]);

  // Fetch appearance settings
  const { 
    data: settings = DEFAULT_SETTINGS,
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['appearanceSettings', retryCount],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[useAppearanceSettingsQuery] Fetching appearance settings (attempt ${retryCount + 1})`);

        const { data, error } = await supabase
          .from('user_settings')
          .select('dark_mode, compact_mode, animations')
          .eq('user_id', userId)
          .maybeSingle();

        if (error) {
          console.error('[useAppearanceSettingsQuery] Error fetching appearance settings:', error);
          throw error;
        }

        if (!data) {
          console.log('[useAppearanceSettingsQuery] No appearance settings found, using defaults');
          return DEFAULT_SETTINGS;
        }

        console.log('[useAppearanceSettingsQuery] Successfully loaded appearance settings:', data);
        return {
          dark_mode: data.dark_mode ?? DEFAULT_SETTINGS.dark_mode,
          compact_mode: data.compact_mode ?? DEFAULT_SETTINGS.compact_mode,
          animations: data.animations ?? DEFAULT_SETTINGS.animations
        };
      } catch (err: any) {
        console.error('[useAppearanceSettingsQuery] Error fetching appearance settings:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: !!userId
  });

  // Mutation for updating a setting
  const updateSettingMutation = useMutation({
    mutationFn: async ({ key, value }: { key: UserSettingKey, value: boolean }) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useAppearanceSettingsQuery] Updating setting: ${key} = ${value}`);

      // Check if user settings exist
      const { data: existingSettings, error: checkError } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', userId)
        .maybeSingle();

      if (checkError) {
        console.error('[useAppearanceSettingsQuery] Error checking existing settings:', checkError);
        throw checkError;
      }

      let result;
      
      if (existingSettings) {
        // Update existing settings
        result = await supabase
          .from('user_settings')
          .update({ [key]: value, updated_at: new Date().toISOString() })
          .eq('user_id', userId);
      } else {
        // Create new settings
        const defaultSettings = {
          user_id: userId,
          dark_mode: DEFAULT_SETTINGS.dark_mode,
          compact_mode: DEFAULT_SETTINGS.compact_mode,
          animations: DEFAULT_SETTINGS.animations,
          email_notifications: true,
          push_notifications: false,
          weekly_summary: true,
          inventory_alerts: true
        };
        
        // Override the default with the new value
        const newSettings = {
          ...defaultSettings,
          [key]: value
        };
        
        // Insert the new settings
        result = await supabase
          .from('user_settings')
          .insert(newSettings);
      }

      if (result.error) {
        console.error('[useAppearanceSettingsQuery] Error updating setting:', result.error);
        throw result.error;
      }

      return true;
    },
    onSuccess: () => {
      // Invalidate and refetch the settings query
      queryClient.invalidateQueries({ queryKey: ['appearanceSettings'] });
    },
    onError: (error) => {
      console.error('[useAppearanceSettingsQuery] Mutation error:', error);
      toast.error('Failed to update setting');
    }
  });

  // Function to update a setting
  const updateSetting = async (key: UserSettingKey, value: boolean): Promise<boolean> => {
    try {
      await updateSettingMutation.mutateAsync({ key, value });
      return true;
    } catch (error) {
      console.error('[useAppearanceSettingsQuery] Error in updateSetting:', error);
      return false;
    }
  };

  // Set up a periodic refresh timer
  useEffect(() => {
    if (!userId) return;

    console.log('[useAppearanceSettingsQuery] Setting up periodic refresh timer');

    // Set up periodic refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      // Only refresh if the document is visible
      if (document.visibilityState === 'visible') {
        console.log('[useAppearanceSettingsQuery] Periodic refresh triggered');
        refreshSettings();
      }
    }, 30000); // 30 seconds

    return () => {
      clearInterval(refreshInterval);
    };
  }, [userId, refreshSettings]);

  return {
    settings,
    loading: isLoading,
    error: error ? (error as Error).message : null,
    isError,
    updateSetting,
    refreshSettings
  };
};
