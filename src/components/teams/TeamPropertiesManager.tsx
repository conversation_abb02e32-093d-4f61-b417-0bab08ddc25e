import React, { useState, useEffect } from 'react';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Property } from '@/hooks/usePropertiesQueryV2';
import { Team } from '@/hooks/useTeamManagement';
import { toast } from 'sonner';

interface TeamPropertiesManagerProps {
  team: Team;
  onPropertiesUpdated?: () => void;
}

export const TeamPropertiesManager: React.FC<TeamPropertiesManagerProps> = ({
  team,
  onPropertiesUpdated
}) => {
  const { properties, loading: propertiesLoading } = usePropertiesQueryV2();
  const {
    teamProperties,
    assignPropertyToTeam,
    unassignPropertyFromTeam,
    fetchTeamProperties,
    loading: teamLoading
  } = useTeamManagement();

  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [availableProperties, setAvailableProperties] = useState<Property[]>([]);

  // Track if component is mounted to prevent state updates after unmount
  const [isMounted, setIsMounted] = useState(true);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Function to load team properties
  const loadTeamProperties = async () => {
    if (!team?.id) return;

    console.log('TeamPropertiesManager: Fetching properties for team', team.id);
    try {
      await fetchTeamProperties(team.id);
    } catch (err) {
      console.error('TeamPropertiesManager - Error fetching team properties:', err);
    }
  };

  // Fetch team properties when the component mounts or team changes
  useEffect(() => {
    if (team?.id) {
      console.log('TeamPropertiesManager: Team ID changed or component mounted, loading properties');
      loadTeamProperties();
    }
  }, [team?.id]);

  // Removed custom event listener for tab changes
  // React Query will handle data refreshing automatically

  // Update local state when teamProperties changes
  useEffect(() => {
    console.log('TeamPropertiesManager: teamProperties updated', teamProperties);
    if (teamProperties && teamProperties.length > 0) {
      setSelectedProperties(teamProperties);
    }
  }, [teamProperties]);

  // Filter properties to only show those owned by the current user
  useEffect(() => {
    if (properties) {
      setAvailableProperties(properties);
    }
  }, [properties]);

  const handlePropertyToggle = async (propertyId: string, isChecked: boolean) => {
    try {
      console.log(`Property ${propertyId} toggle: ${isChecked ? 'checked' : 'unchecked'}`);

      if (isChecked) {
        // Assign property to team
        const success = await assignPropertyToTeam(team.id, propertyId);
        if (success) {
          console.log(`Successfully assigned property ${propertyId} to team ${team.id}`);
          // Optimistically update the UI
          setSelectedProperties(prev => {
            if (!prev.includes(propertyId)) {
              return [...prev, propertyId];
            }
            return prev;
          });
          // No need to call fetchTeamProperties here as it will be triggered by the assignPropertyToTeam function
          if (onPropertiesUpdated) {
            onPropertiesUpdated();
          }
        }
      } else {
        // Unassign property from team
        const success = await unassignPropertyFromTeam(team.id, propertyId);
        if (success) {
          console.log(`Successfully unassigned property ${propertyId} from team ${team.id}`);
          // Optimistically update the UI
          setSelectedProperties(prev => prev.filter(id => id !== propertyId));
          // No need to call fetchTeamProperties here as it will be triggered by the unassignPropertyFromTeam function
          if (onPropertiesUpdated) {
            onPropertiesUpdated();
          }
        }
      }
    } catch (error: any) {
      console.error('Error toggling property:', error);
      toast.error('Failed to update team properties', {
        description: error.message
      });
    }
  };

  const isPropertySelected = (propertyId: string) => {
    // Check both the local state and the global teamProperties state
    return selectedProperties.includes(propertyId) || teamProperties.includes(propertyId);
  };

  const loading = propertiesLoading || teamLoading;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Team Properties</CardTitle>
        <CardDescription>
          Assign properties to this team. Team members will be able to access these properties based on their permissions.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : availableProperties.length === 0 ? (
          <div className="text-center p-4 text-gray-500">
            No properties available to assign to this team.
          </div>
        ) : (
          <div className="space-y-4">
            {availableProperties.map(property => (
              <div key={property.id} className="flex items-start space-x-3 p-2 hover:bg-gray-50 rounded-md">
                <Checkbox
                  id={`property-${property.id}`}
                  checked={isPropertySelected(property.id)}
                  onCheckedChange={(checked) => handlePropertyToggle(property.id, checked === true)}
                />
                <div className="grid gap-1.5">
                  <Label htmlFor={`property-${property.id}`} className="font-medium">
                    {property.name}
                  </Label>
                  <p className="text-sm text-gray-500">
                    {property.address}, {property.city}, {property.state} {property.zip}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TeamPropertiesManager;
