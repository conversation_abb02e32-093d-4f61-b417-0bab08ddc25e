import { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/supabase';
import InviteTeamMemberDialog from './InviteTeamMemberDialog';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { UserPlus, Trash2, Loader2, Users } from 'lucide-react';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';


interface TeamMemberManagementProps {
  teamId: string;
}

const TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({ teamId }) => {
  const { authState } = useAuth();
  const {
    teamMembers,
    teams,
    loading,
    fetchTeamMembers,
    inviteUserToTeam,
    removeTeamMember
  } = useTeamManagement();

  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const { hasPermission } = usePermissions();

  const [isProcessing, setIsProcessing] = useState(false);

  // Get the selected team from useTeamManagement
  const { selectedTeam } = useTeamManagement();

  // Store team ownership status in refs to prevent re-renders and excessive API calls
  const teamOwnershipRef = useRef<{[key: string]: boolean}>({});
  const teamOwnershipCheckedRef = useRef<{[key: string]: boolean}>({});
  const isCheckingOwnershipRef = useRef(false);

  // Optimized team owner check to prevent infinite loops and excessive API calls
  const isTeamOwner = (() => {
    if (!teamId || !authState.user?.id) {
      return false;
    }

    // Create a unique key for this team+user combination
    const ownershipKey = `${teamId}:${authState.user.id}`;

    // If we've already checked this team, return the cached result
    if (teamOwnershipCheckedRef.current[ownershipKey]) {
      return teamOwnershipRef.current[ownershipKey] || false;
    }

    // Check from selectedTeam first - this is the most efficient path
    if (selectedTeam?.id === teamId) {
      const isOwner = selectedTeam.owner_id === authState.user?.id;

      // Cache the result
      teamOwnershipRef.current[ownershipKey] = isOwner;
      teamOwnershipCheckedRef.current[ownershipKey] = true;

      return isOwner;
    }

    // Check from teams array if selectedTeam doesn't match
    if (teams.length > 0) {
      const team = teams.find(t => t.id === teamId);
      if (team) {
        const isOwner = team.owner_id === authState.user?.id;

        // Cache the result
        teamOwnershipRef.current[ownershipKey] = isOwner;
        teamOwnershipCheckedRef.current[ownershipKey] = true;

        return isOwner;
      }
    }

    // If we don't have the team in the array or selectedTeam,
    // set a default value and schedule a single check
    if (!teamOwnershipCheckedRef.current[ownershipKey] && !isCheckingOwnershipRef.current) {
      // Mark as checking to prevent duplicate checks
      isCheckingOwnershipRef.current = true;

      // Set a default value in the cache to prevent repeated checks
      teamOwnershipCheckedRef.current[ownershipKey] = true;
      teamOwnershipRef.current[ownershipKey] = false;

      // Schedule a single check with a delay to prevent excessive API calls
      setTimeout(() => {
        const checkOwnership = async () => {
          try {
            const { data, error } = await supabase
              .from('teams')
              .select('owner_id')
              .eq('id', teamId)
              .single();

            if (error) {
              isCheckingOwnershipRef.current = false;
              return;
            }

            const isOwner = data.owner_id === authState.user?.id;

            // Update the cached result
            teamOwnershipRef.current[ownershipKey] = isOwner;

            // Clear the checking flag
            isCheckingOwnershipRef.current = false;
          } catch (err) {
            isCheckingOwnershipRef.current = false;
          }
        };

        checkOwnership();
      }, 500); // Longer delay to prevent rapid API calls
    }

    // Return the cached value or default to false
    return teamOwnershipRef.current[ownershipKey] || false;
  })();

  const isAdmin = authState?.profile?.role === 'admin' || authState?.profile?.is_super_admin;
  const isPropertyManager = authState?.profile?.role === 'property_manager';

  // Team owners and admins can manage staff and providers
  // Property managers should also be able to invite team members
  const canManageStaff = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_STAFF, teamId);
  const canManageProviders = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_SERVICE_PROVIDERS, teamId);

  // Force enable invite button for property managers
  const canInviteMembers = isTeamOwner || isAdmin || isPropertyManager || canManageStaff || canManageProviders;

  // For debugging
  console.log('TeamMemberManagement - Permissions:', {
    isTeamOwner,
    isAdmin,
    isPropertyManager,
    canManageStaff,
    canManageProviders,
    canInviteMembers,
    teamId,
    selectedTeamId: selectedTeam?.id,
    userId: authState.user?.id,
    teamOwnerId: selectedTeam?.owner_id,
    userEmail: authState.user?.email,
    userRole: authState?.profile?.role
  });

  // Track if component is mounted to prevent state updates after unmount
  const [isMounted, setIsMounted] = useState(true);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Get the query client outside of the function
  const queryClient = useQueryClient();

  // Function to load team members using React Query's built-in functionality
  const loadTeamMembers = async (force = false) => {
    if (!teamId || (loading && !force)) return;

    console.log('TeamMemberManagement - Fetching team members for team:', teamId, force ? '(forced)' : '');
    try {
      // First try a direct database query to ensure we have the latest data
      try {
        console.log('TeamMemberManagement - Trying direct database query first');
        const { data, error } = await supabase
          .from('team_members')
          .select(`
            *,
            profiles:user_id (id, email, first_name, last_name, role)
          `)
          .eq('team_id', teamId);

        if (error) {
          console.error('TeamMemberManagement - Error in direct database query:', error);
        } else if (data && data.length > 0) {
          console.log(`TeamMemberManagement - Direct query found ${data.length} team members`);

          // Log the data for debugging
          console.log('Team members found via direct query:', data.length, data);

          // Format the data to match the expected format
          const formattedMembers = data.map(member => ({
            id: member.id,
            team_id: member.team_id,
            user_id: member.user_id,
            added_by: member.added_by,
            status: member.status,
            created_at: member.created_at,
            updated_at: member.updated_at,
            user_email: member.profiles?.email || '',
            user_name: `${member.profiles?.first_name || ''} ${member.profiles?.last_name || ''}`.trim() || 'Unknown User',
            role: member.profiles?.role || 'user'
          }));

          console.log('Formatted team members:', formattedMembers);
        }
      } catch (dbErr) {
        console.error('TeamMemberManagement - Error in direct database query:', dbErr);
      }

      // Then use the hook's fetch method
      await fetchTeamMembers(teamId);

      // Then invalidate the cache to ensure fresh data
      await queryClient.invalidateQueries({
        queryKey: ['teamMembersV2', teamId],
        exact: false
      });

      await queryClient.refetchQueries({
        queryKey: ['teamMembersV2', teamId],
        exact: false,
        type: 'all'
      });

      if (isMounted) {
        console.log('TeamMemberManagement - Team members fetched successfully through React Query');

        // Log the current team members from the hook
        console.log('TeamMemberManagement - Team members updated:', teamMembers.length);
      }
    } catch (err) {
      console.error('TeamMemberManagement - Error fetching team members:', err);
      if (isMounted) {
        toast({
          title: "Error",
          description: "Failed to load team members. Please try again.",
          variant: "destructive"
        });
      }
    }
  };

  // Track if team members have been loaded for each team ID
  const teamMembersLoadedRef = useRef<{[key: string]: boolean}>({});
  const isLoadingTeamMembersRef = useRef(false);
  const lastTeamIdRef = useRef<string | null>(null);

  // Single useEffect for loading team members to prevent multiple loads and flashing
  useEffect(() => {
    // Only load if we have a teamId
    if (!teamId) return;

    console.log(`TeamMemberManagement - Team ID: ${teamId}, Current members: ${teamMembers.length}`);

    // Always load team members when the component mounts or teamId changes
    const loadData = async () => {
      // Set loading flag
      isLoadingTeamMembersRef.current = true;

      try {
        console.log(`TeamMemberManagement - Loading team members for team ${teamId}`);

        // First try a direct database query to ensure we have the latest data
        try {
          const { data, error } = await supabase
            .from('team_members')
            .select(`
              *,
              profiles:user_id (id, email, first_name, last_name, avatar_url, role)
            `)
            .eq('team_id', teamId);

          if (error) {
            console.error('TeamMemberManagement - Error in direct database query:', error);
          } else if (data && data.length > 0) {
            console.log(`TeamMemberManagement - Direct query found ${data.length} team members`);
          }
        } catch (dbErr) {
          console.error('TeamMemberManagement - Error in direct database query:', dbErr);
        }

        // Then use the hook's fetch method
        await fetchTeamMembers(teamId);

        // Mark as loaded
        teamMembersLoadedRef.current[teamId] = true;
        lastTeamIdRef.current = teamId;

      } catch (err) {
        console.error('TeamMemberManagement - Error fetching team members:', err);
        toast({
          title: "Error",
          description: "Failed to load team members. Please try again.",
          variant: "destructive"
        });
      } finally {
        isLoadingTeamMembersRef.current = false;
      }
    };

    // Load data immediately
    loadData();

  }, [teamId, fetchTeamMembers]);

  // Add a separate effect to log team members when they change
  useEffect(() => {
    console.log('TeamMemberManagement - Team members updated:', teamMembers.length);
    if (teamMembers.length > 0) {
      console.log('TeamMemberManagement - First team member:', teamMembers[0]);
    } else {
      console.log('TeamMemberManagement - No team members found');

      // Debug the team members loading state
      console.log('TeamMemberManagement - Debug info:', {
        loading,
        teamId,
        isTeamOwner,
        selectedTeamId: selectedTeam?.id,
        teamMembersLoadedRef: teamMembersLoadedRef.current,
        isLoadingTeamMembersRef: isLoadingTeamMembersRef.current,
        lastTeamIdRef: lastTeamIdRef.current
      });
    }
  }, [teamMembers, loading, teamId, isTeamOwner, selectedTeam?.id]);

  // Removed custom event listener for tab changes
  // React Query will handle data refreshing automatically

  // Add a separate effect to log team members when they change
  useEffect(() => {
    if (teamMembers.length > 0) {
      console.log('TeamMemberManagement - Team members updated:', teamMembers.length);
    }
  }, [teamMembers]);

  const handleInviteUser = async (email: string, role: UserRole) => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      await inviteUserToTeam(teamId, email, role);
      toast.success(`Invitation sent to ${email}`);
      setInviteDialogOpen(false);
    } catch (error) {
      console.error('Error inviting user:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      const success = await removeTeamMember(memberId);
      if (success) {
        // Reload team members after removal
        await fetchTeamMembers(teamId);
        toast.success('Team member removed successfully');
      }
    } catch (error) {
      console.error('Error removing team member:', error);
      toast.error('Failed to remove team member');
    } finally {
      setIsProcessing(false);
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || '';
    const last = lastName?.charAt(0) || '';
    return (first + last).toUpperCase() || 'U';
  };

  return (
    <Card>
      <CardContent className="p-0">
        <div className="p-4 sm:p-6 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-medium">Team Members</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => loadTeamMembers(true)}
              className="h-8 w-8 p-0"
              title="Refresh team members"
            >
              <Loader2 className="h-4 w-4" />
              <span className="sr-only">Refresh</span>
            </Button>
          </div>
          {canInviteMembers && (
            <Button
              onClick={() => setInviteDialogOpen(true)}
              className="flex items-center gap-2"
              disabled={isProcessing}
            >
              <UserPlus className="h-4 w-4" />
              <span>Invite</span>
            </Button>
          )}
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : teamMembers.length === 0 ? (
          <div className="text-center p-8 border-t">
            <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">No team members yet</p>
            <div className="flex flex-col gap-4 items-center">
              {canInviteMembers && (
                <Button
                  onClick={() => setInviteDialogOpen(true)}
                  variant="outline"
                  disabled={isProcessing}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite Members
                </Button>
              )}

              {/* Debug button to manually load team members */}
              <Button
                onClick={() => loadTeamMembers(true)}
                variant="secondary"
                size="sm"
                className="mt-2"
              >
                <Loader2 className="h-4 w-4 mr-2" />
                Refresh Team Members
              </Button>
            </div>
          </div>
        ) : (
          <div className="overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teamMembers.map((member) => {
                  // Debug log to see what data we have for each member
                  console.log('Rendering team member:', member);

                  return (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.avatar_url || undefined} />
                            <AvatarFallback>
                              {getInitials(
                                member.first_name || (member.profiles?.first_name || ''),
                                member.last_name || (member.profiles?.last_name || '')
                              )}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {member.first_name || member.last_name || member.profiles?.first_name || member.profiles?.last_name
                                ? `${member.first_name || member.profiles?.first_name || ''} ${member.last_name || member.profiles?.last_name || ''}`.trim()
                                : member.user_name || 'Unnamed User'}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {member.email || member.profiles?.email || member.user_email || ''}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {(member.profile_role || member.profiles?.role) === 'property_manager'
                            ? 'Property Manager'
                            : (member.profile_role || member.profiles?.role) === 'service_provider'
                              ? 'Service Provider'
                              : member.profile_role || member.profiles?.role || 'User'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className={`h-2 w-2 rounded-full mr-2 ${member.status === 'active' ? 'bg-green-500' : 'bg-amber-500'}`}></span>
                          <span className="capitalize">{member.status}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {canInviteMembers && (member.user_id !== authState.user?.id) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveMember(member.id)}
                            className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                            disabled={isProcessing}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Remove</span>
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      <InviteTeamMemberDialog
        open={inviteDialogOpen}
        onOpenChange={setInviteDialogOpen}
        onInvite={handleInviteUser}
        canInviteStaff={isTeamOwner || isAdmin || isPropertyManager || canManageStaff}
        canInviteProviders={isTeamOwner || isAdmin || isPropertyManager || canManageProviders}
        isTeamOwner={isTeamOwner}
        isAdmin={isAdmin}
        isPropertyManager={isPropertyManager}
        isLoading={isProcessing}
      />
    </Card>
  );
};

export default TeamMemberManagement;
