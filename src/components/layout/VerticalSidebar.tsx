import React, { useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Building2,
  Wrench,
  Package,
  ShoppingCart,
  AlertTriangle,
  Users,
  Settings,
  BarChart3,
  LogOut,
  User,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { PermissionType } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/shadcn-tooltip';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';

interface NavItem {
  name: string;
  path: string;
  icon: React.ElementType;
  requiredPermission?: PermissionType;
  alwaysShow?: boolean;
}

interface VerticalSidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

const VerticalSidebar: React.FC<VerticalSidebarProps> = ({
  collapsed = false,
  onToggleCollapse
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { hasPermission, isAdmin } = usePermissions();
  const { refreshRouteData } = useNavigationRefresh();

  const isActiveRoute = (path: string) => location.pathname === path ||
    (path !== '/' && location.pathname.startsWith(path));

  // Custom navigation handler that refreshes data when navigating
  const handleNavigation = useCallback((path: string, e: React.MouseEvent) => {
    e.preventDefault(); // Prevent default link behavior

    // Ensure path has the correct format for hash routing
    const hashedPath = path.startsWith('/') ? `/#${path}` : `/#/${path}`;
    console.log(`[VerticalSidebar] Navigating to ${hashedPath} with data refresh`);

    // First refresh the data for the route
    refreshRouteData(path);

    // Then navigate to the route
    navigate(path);

    // We no longer need to dispatch a custom event here as refreshRouteData already handles this
    // This prevents duplicate refreshes
  }, [navigate, refreshRouteData, location.pathname]);

  // Define navigation items with required permissions
  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: LayoutDashboard,
      alwaysShow: true // Everyone can see dashboard
    },
    {
      name: 'Operations',
      path: '/operations',
      icon: BarChart3,
      alwaysShow: true // Everyone can see operations
    },
    {
      name: 'Properties',
      path: '/properties',
      icon: Building2,
      requiredPermission: PermissionType.MANAGE_PROPERTIES
    },
    {
      name: 'Maintenance',
      path: '/maintenance',
      icon: Wrench,
      requiredPermission: PermissionType.VIEW_MAINTENANCE
    },
    {
      name: 'Task Automation',
      path: '/maintenance/automation',
      icon: Calendar,
      requiredPermission: PermissionType.VIEW_MAINTENANCE
    },
    {
      name: 'Inventory',
      path: '/inventory',
      icon: Package,
      requiredPermission: PermissionType.VIEW_INVENTORY
    },
    {
      name: 'Purchase Orders',
      path: '/purchase-orders',
      icon: ShoppingCart,
      requiredPermission: PermissionType.VIEW_PURCHASE_ORDERS
    },
    {
      name: 'Damages',
      path: '/damages',
      icon: AlertTriangle,
      requiredPermission: PermissionType.VIEW_DAMAGE_REPORTS
    },
    {
      name: 'Teams',
      path: '/teams',
      icon: Users,
      requiredPermission: PermissionType.VIEW_TEAM
    },
    // Admin link - only for super admins
    {
      name: 'Admin',
      path: '/admin',
      icon: User,
      // No permission required - we'll filter it manually
    },
    {
      name: 'Settings',
      path: '/settings',
      icon: Settings,
      alwaysShow: true // Everyone can see settings
    }
  ];

  // Filter navigation items based on user permissions
  const visibleNavItems = navigationItems.filter(item => {
    // Special case for Admin link - only super admins can see it
    if (item.name === 'Admin') {
      return authState.profile?.is_super_admin === true;
    }

    // Always show items marked as alwaysShow
    if (item.alwaysShow) return true;

    // Admins can see everything
    if (isAdmin()) return true;

    // Property managers can see everything
    if (authState.profile?.role === 'property_manager') return true;

    // Service providers should see all relevant navigation items
    if (authState.profile?.role === 'service_provider') {
      // Always show these items for service providers
      if (['Dashboard', 'Properties', 'Maintenance', 'Inventory', 'Purchase Orders', 'Damages', 'Teams'].includes(item.name)) {
        return true;
      }
    }

    // Check specific permissions for other users
    if (item.requiredPermission) {
      return hasPermission(item.requiredPermission);
    }

    return false;
  });

  // Remove settings from main navigation and add it to the bottom
  const mainNavItems = visibleNavItems.filter(item => item.name !== 'Settings');
  const settingsItem = visibleNavItems.find(item => item.name === 'Settings');

  const { signOut } = useAuth();
  const handleSignOut = async () => {
    try {
      console.log('[VerticalSidebar] Starting sign out process');

      // First, call the signOut function from AuthContext
      // This will handle all the session cleanup and state updates
      try {
        await signOut();
        console.log('[VerticalSidebar] AuthContext signOut completed successfully');
      } catch (signOutError) {
        console.warn('[VerticalSidebar] AuthContext signOut had issues, continuing with redirect:', signOutError);
      }

      // Regardless of the signOut result, proceed with redirect to login page
      console.log('[VerticalSidebar] Redirecting to login page');

      // Add a small delay to ensure all state updates have completed
      setTimeout(() => {
        try {
          // Use the most reliable method for redirection
          window.location.href = `${window.location.origin}/#/login?source=sidebar`;
          console.log('[VerticalSidebar] Redirect initiated');
        } catch (redirectError) {
          console.error('[VerticalSidebar] Error with redirect:', redirectError);

          // As a last resort, try a different approach
          try {
            window.location.replace('/#/login?source=sidebar');
          } catch (replaceError) {
            console.error('[VerticalSidebar] All redirect attempts failed:', replaceError);
          }
        }
      }, 100); // Small delay to ensure state updates are complete
    } catch (error) {
      console.error('[VerticalSidebar] Unexpected error during sign out:', error);

      // Force redirect even on error
      window.location.href = `${window.location.origin}/#/login?source=sidebar`;
    }
  };

  return (
    <aside className={cn(
      "sidebar h-screen flex-shrink-0 transition-all duration-300 overflow-y-auto flex flex-col fixed top-0 left-0 z-40 bg-blue-700",
      collapsed ? "w-16" : "w-64"
    )} style={{ width: collapsed ? '4rem' : '16rem' }}>
      <div className="p-4 flex items-center justify-between border-b border-blue-600">
        <a href="/#/dashboard" className="flex items-center gap-2" onClick={(e) => handleNavigation('/dashboard', e)}>
          <img
            src="/icons/logo.png"
            alt="StayFu Logo"
            className="h-8 w-8 object-contain bg-white rounded-full p-1"
          />
          {!collapsed && <span className="text-lg font-semibold text-white">StayFu</span>}
        </a>
        {!collapsed ? (
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-blue-600"
            onClick={onToggleCollapse}
          >
            <span className="sr-only">Collapse sidebar</span>
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="white" fillRule="evenodd" clipRule="evenodd"></path>
            </svg>
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-blue-600"
            onClick={onToggleCollapse}
          >
            <span className="sr-only">Expand sidebar</span>
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6.1584 3.13514C5.95694 3.32401 5.94673 3.64042 6.1356 3.84188L9.565 7.49991L6.1356 11.1579C5.94673 11.3594 5.95694 11.6758 6.1584 11.8647C6.35985 12.0535 6.67627 12.0433 6.86514 11.8419L10.6151 7.84188C10.7954 7.64955 10.7954 7.35027 10.6151 7.15794L6.86514 3.15794C6.67627 2.95648 6.35985 2.94628 6.1584 3.13514Z" fill="white" fillRule="evenodd" clipRule="evenodd"></path>
            </svg>
          </Button>
        )}
      </div>

      {/* Main navigation */}
      <nav className="p-3 flex-grow">
        <ul className="space-y-1">
          {mainNavItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.path}>
                {collapsed ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={item.path.startsWith('/') ? `/#${item.path}` : `/#/${item.path}`}
                          className={cn(
                            "flex items-center justify-center px-3 py-2 rounded-md transition-colors",
                            isActiveRoute(item.path)
                              ? "bg-white/20 text-white"
                              : "text-white/80 hover:bg-white/10 hover:text-white"
                          )}
                          onClick={(e) => handleNavigation(item.path, e)}
                        >
                          <Icon size={20} />
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <p>{item.name}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <a
                    href={item.path.startsWith('/') ? `/#${item.path}` : `/#/${item.path}`}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                      isActiveRoute(item.path)
                        ? "bg-white/20 text-white"
                        : "text-white/80 hover:bg-white/10 hover:text-white"
                    )}
                    onClick={(e) => handleNavigation(item.path, e)}
                  >
                    <Icon size={20} />
                    <span className="text-sm font-medium">{item.name}</span>
                  </a>
                )}
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User profile and settings at bottom */}
      <div className="mt-auto border-t border-blue-600 p-3">
        <ul className="space-y-1">
          {settingsItem && (
            <li key={settingsItem.path}>
              {collapsed ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <a
                        href={settingsItem.path.startsWith('/') ? `/#${settingsItem.path}` : `/#/${settingsItem.path}`}
                        className={cn(
                          "flex items-center justify-center px-3 py-2 rounded-md transition-colors",
                          isActiveRoute(settingsItem.path)
                            ? "bg-white/20 text-white"
                            : "text-white/80 hover:bg-white/10 hover:text-white"
                        )}
                        onClick={(e) => handleNavigation(settingsItem.path, e)}
                      >
                        <Settings size={20} />
                      </a>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>Settings</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <a
                  href={settingsItem.path.startsWith('/') ? `/#${settingsItem.path}` : `/#/${settingsItem.path}`}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                    isActiveRoute(settingsItem.path)
                      ? "bg-white/20 text-white"
                      : "text-white/80 hover:bg-white/10 hover:text-white"
                  )}
                  onClick={(e) => handleNavigation(settingsItem.path, e)}
                >
                  <Settings size={20} />
                  <span className="text-sm font-medium">Settings</span>
                </a>
              )}
            </li>
          )}

          <li>
            {collapsed ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={handleSignOut}
                      className="w-full flex items-center justify-center px-3 py-2 rounded-md transition-colors text-white/80 hover:bg-white/10 hover:text-white"
                      aria-label="Sign Out"
                    >
                      <LogOut size={20} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>Sign Out</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <button
                onClick={handleSignOut}
                className="w-full flex items-center gap-3 px-3 py-2 rounded-md transition-colors text-white/80 hover:bg-white/10 hover:text-white"
                aria-label="Sign Out"
              >
                <LogOut size={20} />
                <span className="text-sm font-medium">Sign Out</span>
              </button>
            )}
          </li>

          <li className="pt-2">
            {collapsed ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <a href="/#/settings/account" className="flex items-center justify-center px-3 py-2" onClick={(e) => handleNavigation('/settings/account', e)}>
                      <Avatar className="h-8 w-8 bg-blue-600 ring-2 ring-white/30">
                        {authState.profile?.avatar_url ? (
                          <AvatarImage src={authState.profile.avatar_url} alt={authState.profile?.first_name || 'User'} />
                        ) : null}
                        <AvatarFallback>{authState.profile?.first_name?.charAt(0) || 'U'}</AvatarFallback>
                      </Avatar>
                    </a>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{authState.profile?.first_name || 'User'}</p>
                    <p className="text-xs opacity-70">
                      {authState.profile?.role === 'property_manager' ? 'Property Manager' :
                       authState.profile?.role === 'super_admin' ? 'Super Admin' :
                       authState.profile?.role === 'admin' ? 'Admin' :
                       authState.profile?.role === 'service_provider' ? 'Service Provider' :
                       authState.profile?.role === 'staff' ? 'Staff' :
                       authState.profile?.role || 'User'}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <div className="flex items-center gap-3 px-3 py-2">
                <a href="/#/settings/account" onClick={(e) => handleNavigation('/settings/account', e)}>
                  <Avatar className="h-8 w-8 bg-blue-600 ring-2 ring-white/30">
                    {authState.profile?.avatar_url ? (
                      <AvatarImage src={authState.profile.avatar_url} alt={authState.profile?.first_name || 'User'} />
                    ) : null}
                    <AvatarFallback>{authState.profile?.first_name?.charAt(0) || 'U'}</AvatarFallback>
                  </Avatar>
                </a>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-white">
                    {authState.profile?.first_name || 'User'}
                  </span>
                  <span className="text-xs text-white/70">
                    {authState.profile?.role === 'property_manager' ? 'Property Manager' :
                     authState.profile?.role === 'super_admin' ? 'Super Admin' :
                     authState.profile?.role === 'admin' ? 'Admin' :
                     authState.profile?.role === 'service_provider' ? 'Service Provider' :
                     authState.profile?.role === 'staff' ? 'Staff' :
                     authState.profile?.role || 'User'}
                  </span>
                </div>
              </div>
            )}
          </li>
        </ul>
      </div>
    </aside>
  );
};

export default VerticalSidebar;
