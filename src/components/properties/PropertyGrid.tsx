
import React, { useEffect } from 'react';
import { Property } from '@/hooks/useProperties';
import PropertyCard from './PropertyCard';
import { mapPropertyToCardProperty } from '@/utils/propertyUtils';

interface PropertyGridProps {
  properties: Property[];
  onPropertyClick: (property: Property) => void;
}

const PropertyGrid: React.FC<PropertyGridProps> = ({ properties, onPropertyClick }) => {
  useEffect(() => {
    // Debug logging for property images
    console.log('[PropertyGrid] Properties count:', properties.length);
    properties.forEach(property => {
      console.log(`[PropertyGrid] Property ${property.name} image_url:`, property.image_url);
      console.log(`[PropertyGrid] Property ${property.name} bedrooms:`, property.bedrooms);
      console.log(`[PropertyGrid] Property ${property.name} bathrooms:`, property.bathrooms);
    });
  }, [properties]);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {properties.map((property) => {
        const cardProperty = mapPropertyToCardProperty(property);
        console.log(`[PropertyGrid] Mapped property ${property.name}:`, {
          imageUrl: cardProperty.imageUrl,
          bedrooms: cardProperty.bedrooms,
          bathrooms: cardProperty.bathrooms
        });

        return (
          <div key={property.id} onClick={() => onPropertyClick(property)} className="cursor-pointer">
            <PropertyCard
              property={cardProperty}
              onView={() => onPropertyClick(property)}
            />
          </div>
        );
      })}
    </div>
  );
};

export default PropertyGrid;
