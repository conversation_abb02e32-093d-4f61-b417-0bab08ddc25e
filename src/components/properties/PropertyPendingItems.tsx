
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON>ch, 
  AlertTriangle, 
  ShoppingCart, 
  ListTodo, 
  Plus, 
  Loader2 
} from 'lucide-react';
import { PendingItem } from '@/hooks/usePendingItems';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface PropertyPendingItemsProps {
  pendingItems: PendingItem[];
  propertyId: string;
  loading: boolean;
}

const PropertyPendingItems: React.FC<PropertyPendingItemsProps> = ({
  pendingItems,
  propertyId,
  loading
}) => {
  const navigate = useNavigate();

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-blue-500" />;
      case 'damage':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'inventory':
        return <ShoppingCart className="h-4 w-4 text-purple-500" />;
      default:
        return <ListTodo className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string, type: string) => {
    if (type === 'inventory') return 'bg-purple-100 text-purple-800';
    
    switch (status) {
      case 'new':
      case 'open':
        return 'bg-amber-100 text-amber-800';
      case 'assigned':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
      case 'closed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleViewItem = (item: PendingItem) => {
    switch (item.type) {
      case 'maintenance':
        navigate(`/maintenance?task=${item.id}`);
        break;
      case 'damage':
        navigate(`/damages/${item.id}`);
        break;
      case 'inventory':
        navigate(`/inventory?focus=${item.id}`);
        break;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pending Items</CardTitle>
        <CardDescription>
          View all maintenance tasks, damage reports, and inventory needs for this property
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : pendingItems.length > 0 ? (
          <div className="space-y-2">
            {pendingItems.map((item) => (
              <div 
                key={`${item.type}-${item.id}`} 
                className="flex justify-between items-center p-3 border rounded-lg cursor-pointer hover:bg-muted/50"
                onClick={() => handleViewItem(item)}
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-muted">
                    {getItemIcon(item.type)}
                  </div>
                  <div>
                    <p className="font-medium">{item.title}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getStatusColor(item.status, item.type)}>
                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                      </Badge>
                      {item.priority && (
                        <Badge variant="outline">
                          {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)} Priority
                        </Badge>
                      )}
                      <span className="text-xs text-muted-foreground">
                        {new Date(item.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="sm">View</Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-muted/30 rounded-lg">
            <p className="text-muted-foreground">No pending items for this property.</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => navigate(`/maintenance?property=${propertyId}`)}>
          <Plus className="mr-2 h-4 w-4" />
          New Maintenance
        </Button>
        <Button variant="outline" onClick={() => navigate(`/damages/add?property=${propertyId}`)}>
          <Plus className="mr-2 h-4 w-4" />
          Report Damage
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PropertyPendingItems;
