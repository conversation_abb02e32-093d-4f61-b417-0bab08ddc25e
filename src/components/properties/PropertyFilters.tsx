
import React from 'react';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

export interface FilterState {
  searchTerm?: string;
  city?: string;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
}

interface PropertyFiltersProps {
  searchQuery?: string;
  setSearchQuery?: (query: string) => void;
  handleFilterClick?: () => void;
  filters?: FilterState;
  onFilterChange?: React.Dispatch<React.SetStateAction<FilterState>>;
  cities?: string[];
}

const PropertyFilters: React.FC<PropertyFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  handleFilterClick,
  filters = {
    searchTerm: '',
    city: '',
    minBedrooms: 0,
    maxBedrooms: 10,
    minBathrooms: 0,
    maxBathrooms: 10
  },
  onFilterChange,
  cities = []
}) => {
  const handleCityChange = (city: string) => {
    if (onFilterChange) {
      onFilterChange(prev => ({ ...prev, city }));
    }
  };

  const handleBedroomSliderChange = (value: number[]) => {
    if (onFilterChange) {
      onFilterChange(prev => ({
        ...prev,
        minBedrooms: value[0],
        maxBedrooms: value[1]
      }));
    }
  };

  const handleBathroomSliderChange = (value: number[]) => {
    if (onFilterChange) {
      onFilterChange(prev => ({
        ...prev,
        minBathrooms: value[0],
        maxBathrooms: value[1]
      }));
    }
  };

  const handleResetFilters = () => {
    if (onFilterChange) {
      onFilterChange({
        searchTerm: '',
        city: '',
        minBedrooms: 0,
        maxBedrooms: 10,
        minBathrooms: 0,
        maxBathrooms: 10
      });
    }
    if (setSearchQuery) {
      setSearchQuery('');
    }
  };

  return (
    <motion.div 
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: 'auto', opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="mb-6 p-4 bg-card rounded-lg border shadow-sm"
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label className="mb-2 block">City</Label>
          <Select value={filters.city} onValueChange={handleCityChange}>
            <SelectTrigger>
              <SelectValue placeholder="All cities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_cities">All cities</SelectItem>
              {cities.map((city) => (
                <SelectItem key={city} value={city}>
                  {city}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <div className="flex justify-between mb-2">
            <Label>Bedrooms</Label>
            <span className="text-sm text-muted-foreground">
              {filters.minBedrooms || 0} - {filters.maxBedrooms || 10}+
            </span>
          </div>
          <Slider 
            defaultValue={[filters.minBedrooms || 0, filters.maxBedrooms || 10]} 
            min={0} 
            max={10} 
            step={1}
            onValueChange={handleBedroomSliderChange}
          />
        </div>
        
        <div>
          <div className="flex justify-between mb-2">
            <Label>Bathrooms</Label>
            <span className="text-sm text-muted-foreground">
              {filters.minBathrooms || 0} - {filters.maxBathrooms || 10}+
            </span>
          </div>
          <Slider 
            defaultValue={[filters.minBathrooms || 0, filters.maxBathrooms || 10]} 
            min={0} 
            max={10} 
            step={1} 
            onValueChange={handleBathroomSliderChange}
          />
        </div>
      </div>
      
      <div className="flex justify-end mt-4">
        <Button 
          variant="outline" 
          onClick={handleResetFilters}
        >
          Reset Filters
        </Button>
      </div>
    </motion.div>
  );
};

export default PropertyFilters;
