
import React from 'react';
import { Grid3x3, LayoutList, Loader2, <PERSON>otateCcw, Plus, Building2, Filter } from 'lucide-react';
import { Button } from '../ui/button';
import { Property } from '@/hooks/useProperties';
import PropertyCard from './PropertyCard';
import PropertyGrid from './PropertyGrid';
import PropertyList from './PropertyList';
import { mapPropertyToCardProperty } from '@/utils/propertyUtils';
import { StandardEmptyState, StandardLoadingState, StandardErrorState } from '@/components/ui/StandardizedUI';

interface PropertyListViewProps {
  loading: boolean;
  error: string | null;
  filteredProperties: Property[];
  properties: Property[];
  handleViewProperty: (property: Property) => void;
  handleRetry: () => void;
  onAddProperty: () => void;
  viewType?: 'grid' | 'list';
}

const PropertyListView: React.FC<PropertyListViewProps> = ({
  loading,
  error,
  filteredProperties,
  properties,
  handleViewProperty,
  handleRetry,
  onAddProperty,
  viewType = 'grid'
}) => {
  // Debug logging
  console.log('[PropertyListView] Rendering with:', {
    loading,
    error,
    propertiesLength: properties.length,
    filteredPropertiesLength: filteredProperties.length,
    viewType
  });
  if (loading) {
    return <StandardLoadingState message="Loading properties..." />;
  }

  if (error) {
    return <StandardErrorState message="Error loading properties" onRetry={handleRetry} />;
  }

  if (properties.length === 0) {
    return (
      <StandardEmptyState
        title="No properties yet"
        description="Add your first property to get started"
        actionLabel="Add Property"
        onAction={onAddProperty}
        icon={<Building2 className="h-12 w-12 text-muted-foreground" />}
      />
    );
  }

  if (filteredProperties.length === 0) {
    return (
      <StandardEmptyState
        title="No matching properties"
        description="Try adjusting your filters"
        icon={<Filter className="h-12 w-12 text-muted-foreground" />}
      />
    );
  }

  return (
    <div className="mt-8">
      {viewType === 'list' ? (
        <PropertyList
          properties={filteredProperties}
          onPropertyClick={handleViewProperty}
        />
      ) : (
        <PropertyGrid
          properties={filteredProperties}
          onPropertyClick={handleViewProperty}
        />
      )}
    </div>
  );
};

export default PropertyListView;
