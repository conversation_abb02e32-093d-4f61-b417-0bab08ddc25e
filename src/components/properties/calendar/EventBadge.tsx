
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, ListTodo, AlertTriangle, CalendarIcon } from 'lucide-react';

interface EventBadgeProps {
  type: string;
}

const EventBadge: React.FC<EventBadgeProps> = ({ type }) => {
  switch (type) {
    case 'booking':
      return (
        <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">
          <ShoppingCart className="h-3 w-3 mr-1" />
          Booking
        </Badge>
      );
    case 'maintenance':
      return (
        <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
          <ListTodo className="h-3 w-3 mr-1" />
          Maintenance
        </Badge>
      );
    case 'damage':
      return (
        <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Damage
        </Badge>
      );
    default:
      return (
        <Badge variant="outline">
          <CalendarIcon className="h-3 w-3 mr-1" />
          Event
        </Badge>
      );
  }
};

export default EventBadge;
