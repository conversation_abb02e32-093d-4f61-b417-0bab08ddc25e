
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { CalendarEvent } from './types';
import {
  fetchIcalBookings,
  fetchDbBookings,
  fetchMaintenanceTasks,
  fetchDamageReports
} from './eventHelpers';

/**
 * Hook to fetch and manage calendar events for a property
 * @param propertyId The ID of the property to fetch events for
 * @returns Object containing events and loading state
 */
export const useCalendarEvents = (propertyId: string) => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!propertyId) return;

    const fetchEvents = async () => {
      setLoading(true);
      let allEvents: CalendarEvent[] = [];

      try {
        console.log(`Fetching calendar events for property: ${propertyId}`);
        
        // Fetch property for iCal URL
        const { data: property, error: propertyError } = await supabase
          .from('properties')
          .select('*')
          .eq('id', propertyId)
          .single();

        if (propertyError) throw propertyError;

        console.log(`Property data fetched:`, {
          id: property.id,
          name: property.name,
          ical_url: property.ical_url ? 'exists' : 'missing',
          next_booking: property.next_booking,
          is_occupied: property.is_occupied,
          current_checkout: property.current_checkout
        });

        // If we have ical_url but no next_booking, trigger a sync
        if (property.ical_url && !property.next_booking) {
          console.log(`Property has iCal URL but no next_booking, triggering sync`);
          const { data: syncData } = await supabase.functions.invoke('sync-property-calendars', {
            body: { propertyId }
          });
          
          console.log(`Calendar sync result:`, syncData);
          
          if (syncData?.success) {
            // Refetch property after sync
            const { data: refreshedProperty } = await supabase
              .from('properties')
              .select('*')
              .eq('id', propertyId)
              .single();
              
            if (refreshedProperty) {
              property.next_booking = refreshedProperty.next_booking;
              property.is_occupied = refreshedProperty.is_occupied;
              property.current_checkout = refreshedProperty.current_checkout;
              console.log(`Property data refreshed after sync:`, {
                next_booking: property.next_booking,
                is_occupied: property.is_occupied,
                current_checkout: property.current_checkout
              });
            }
          }
        }

        // Fetch all event types
        console.log(`Fetching calendar events for property ${property.id}`);
        
        const icalBookings = await fetchIcalBookings(property);
        console.log(`Fetched ${icalBookings.length} iCal bookings`);
        
        const dbBookings = await fetchDbBookings(propertyId);
        console.log(`Fetched ${dbBookings.length} database bookings`);
        
        const maintenanceTasks = await fetchMaintenanceTasks(propertyId);
        console.log(`Fetched ${maintenanceTasks.length} maintenance tasks`);
        
        const damageReports = await fetchDamageReports(propertyId);
        console.log(`Fetched ${damageReports.length} damage reports`);

        allEvents = [
          ...icalBookings,
          ...dbBookings,
          ...maintenanceTasks,
          ...damageReports
        ];

        console.log(`Total events for property: ${allEvents.length}`);
        
        // For debugging, output all booking events
        const bookingEvents = allEvents.filter(e => e.type === 'booking');
        if (bookingEvents.length > 0) {
          console.log('DEBUG: Booking events for this property:', bookingEvents
            .map(event => ({
              title: event.title,
              startDate: event.date.toLocaleDateString(),
              endDate: event.endDate ? event.endDate.toLocaleDateString() : 'N/A'
            }))
          );
        } else {
          console.log('DEBUG: No booking events found for this property');
        }

        setEvents(allEvents);
      } catch (error) {
        console.error('Error fetching calendar events:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [propertyId]);

  return { events, loading };
};
