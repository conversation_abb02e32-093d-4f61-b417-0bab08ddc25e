
import React from 'react';
import { CalendarEvent } from './types';
import EventBadge from './EventBadge';

interface EventListProps {
  events: CalendarEvent[];
  loading: boolean;
}

const EventList: React.FC<EventListProps> = ({ events, loading }) => {
  return (
    <div className="space-y-2 max-h-[250px] overflow-y-auto">
      {loading ? (
        <div className="text-center py-4 text-muted-foreground">Loading events...</div>
      ) : events.length > 0 ? (
        events.map(event => (
          <div 
            key={event.id} 
            className="p-3 border rounded-md hover:bg-muted/50 transition-colors"
          >
            <div className="flex justify-between">
              <span className="font-medium">{event.title}</span>
              <EventBadge type={event.type} />
            </div>
            {event.status && (
              <div className="mt-1 text-xs text-muted-foreground">
                Status: {event.status.replace(/_/g, ' ')}
                {event.priority && ` • Priority: ${event.priority}`}
              </div>
            )}
            {event.type === 'booking' && event.endDate && (
              <div className="mt-1 text-xs text-muted-foreground">
                {new Date(event.date).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
              </div>
            )}
          </div>
        ))
      ) : (
        <div className="text-center py-4 text-muted-foreground">
          No events for this date
        </div>
      )}
    </div>
  );
};

export default EventList;
