
import React, { useState } from 'react';
import { MaintenanceTask, MaintenanceStatus } from './types';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MoreHorizontal, CheckCircle, XCircle, AlertTriangle, Clock, CircleSlash, ThumbsUp, ThumbsDown } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface MaintenanceCardProps {
  task: MaintenanceTask;
  onClick?: () => void;
  onStatusChange?: (taskId: string, newStatus: MaintenanceStatus) => Promise<boolean>;
  onDeleteTask?: (taskId: string) => Promise<boolean>;
}

const MaintenanceCard: React.FC<MaintenanceCardProps> = ({
  task,
  onClick,
  onStatusChange,
  onDeleteTask
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const { authState } = useAuth();

  const handleStatusChange = async (newStatus: MaintenanceStatus) => {
    if (!onStatusChange) return;

    setIsUpdating(true);
    try {
      const success = await onStatusChange(task.id, newStatus);
      if (success) {
        toast.success(`Task marked as ${newStatus}`);
      } else {
        toast.error('Failed to update task status');
      }
    } catch (error) {
      console.error('Error updating task status:', error);
      toast.error('Failed to update task status');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!onDeleteTask) return;

    if (window.confirm('Are you sure you want to delete this task?')) {
      setIsUpdating(true);
      try {
        const success = await onDeleteTask(task.id);
        if (success) {
          toast.success('Task deleted successfully');
        } else {
          toast.error('Failed to delete task');
        }
      } catch (error) {
        console.error('Error deleting task:', error);
        toast.error('Failed to delete task');
      } finally {
        setIsUpdating(false);
      }
    }
  };

  const getSeverityIcon = () => {
    switch (task.severity) {
      case 'low':
        return <Clock className="h-5 w-5 text-green-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadgeColor = () => {
    switch (task.status) {
      case 'new': return 'bg-blue-500 hover:bg-blue-600';
      case 'assigned': return 'bg-amber-500 hover:bg-amber-600';
      case 'in_progress': return 'bg-purple-500 hover:bg-purple-600';
      case 'completed': return 'bg-green-500 hover:bg-green-600';
      case 'cancelled': return 'bg-gray-500 hover:bg-gray-600';
      case 'accepted': return 'bg-emerald-500 hover:bg-emerald-600';
      case 'rejected': return 'bg-red-500 hover:bg-red-600';
      default: return 'bg-gray-500 hover:bg-gray-600';
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={onClick}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              {getSeverityIcon()}
            </div>
            <div>
              <h3 className="font-medium">{task.title}</h3>
              <p className="text-sm text-gray-500 line-clamp-2">{task.description}</p>

              <div className="flex flex-wrap items-center mt-2 gap-2">
                <Badge className={getStatusBadgeColor()}>
                  {task.status}
                </Badge>

                {task.propertyName && (
                  <span className="text-xs bg-gray-100 dark:bg-gray-800 dark:text-gray-300 px-2 py-1 rounded">
                    {task.propertyName}
                  </span>
                )}

                {task.dueDate && task.dueDate !== 'No due date' && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Due: {task.dueDate}
                  </span>
                )}
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm" disabled={isUpdating}>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {/* Status options available to all users */}
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('completed'); }}>
                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                Mark as Completed
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('in_progress'); }}>
                <Clock className="h-4 w-4 mr-2 text-purple-500" />
                Mark as In Progress
              </DropdownMenuItem>

              {/* Options only available to non-service providers */}
              {authState?.profile?.role !== 'service_provider' && (
                <>
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange('cancelled'); }}>
                    <CircleSlash className="h-4 w-4 mr-2 text-gray-500" />
                    Cancel Task
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete();
                    }}
                    className="text-red-500 focus:text-red-500"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
};

export default MaintenanceCard;
