
import React, { useState, useEffect } from 'react';
import { Package, ShoppingCart, AlertTriangle, Trash2, Image as ImageIcon } from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import { cn } from '@/lib/utils';
import { FormattedInventoryItem } from '@/types/inventory';
import { Button } from '@/components/ui/button';

interface InventoryCardProps {
  item: FormattedInventoryItem;
  onClick: () => void;
  selected?: boolean;
  showDelete?: boolean;
  onDeleteClick?: (e: React.MouseEvent) => void;
}

const InventoryCard: React.FC<InventoryCardProps> = ({
  item,
  onClick,
  selected = false,
  showDelete = false,
  onDeleteClick
}) => {
  const lowStock = item.quantity < item.minQuantity;
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(!!item.imageUrl);
  const [imageSrc, setImageSrc] = useState<string | null>(item.imageUrl || null);

  // Preload and process image
  useEffect(() => {
    if (!item.imageUrl) {
      setImageError(true);
      setImageLoading(false);
      return;
    }

    // Start with the original image URL
    let imageToTry = item.imageUrl;
    setImageSrc(imageToTry);

    // Create an image element to test loading
    const img = new Image();

    // Set up load and error handlers
    img.onload = () => {
      setImageSrc(imageToTry);
      setImageLoading(false);
      setImageError(false);
    };

    img.onerror = () => {
      // Try different fallback strategies

      // 1. Check if it's a Supabase URL
      const isSupabaseUrl = item.imageUrl?.includes('supabase.co/storage/v1/object/public');
      if (isSupabaseUrl && item.imageUrl) {
        try {
          // Try to use the URL directly without the Supabase storage prefix
          const urlParts = item.imageUrl.split('/storage/v1/object/public/');
          if (urlParts.length > 1) {
            imageToTry = `${urlParts[0]}/storage/v1/object/public/${urlParts[1]}`;
            console.log('Trying direct Supabase URL:', imageToTry);
            img.src = imageToTry;
            return; // Exit early to give the new URL a chance to load
          }
        } catch (urlError) {
          console.error('Error fixing Supabase URL:', urlError);
        }
      }

      // 2. Check if it's an Amazon URL
      const isAmazonUrl = item.imageUrl?.includes('amazon.com') || item.imageUrl?.includes('amazon.co');
      if (isAmazonUrl && item.amazonUrl) {
        try {
          // Extract the ASIN from the Amazon URL
          const asinMatch = item.amazonUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
          if (asinMatch && asinMatch[1]) {
            const asin = asinMatch[1];
            imageToTry = `https://m.media-amazon.com/images/I/${asin}.jpg`;
            console.log('Trying Amazon image URL:', imageToTry);
            img.src = imageToTry;
            return; // Exit early to give the new URL a chance to load
          }
        } catch (urlError) {
          console.error('Error creating Amazon image URL:', urlError);
        }
      }

      // 3. Try to extract ASIN from the item name for Amazon products
      if (!isAmazonUrl && item.name && !item.amazonUrl) {
        try {
          // Look for common Amazon product name patterns
          const asinMatch = item.name.match(/\b([A-Z0-9]{10})\b/);
          if (asinMatch && asinMatch[1]) {
            const asin = asinMatch[1];
            imageToTry = `https://m.media-amazon.com/images/I/${asin}.jpg`;
            console.log('Trying Amazon image URL from name:', imageToTry);
            img.src = imageToTry;
            return; // Exit early to give the new URL a chance to load
          }
        } catch (nameError) {
          console.error('Error extracting ASIN from name:', nameError);
        }
      }

      // If all fallbacks fail, use a placeholder
      imageToTry = "/placeholder.svg";
      setImageSrc(imageToTry);
      setImageError(true);
      setImageLoading(false);
    };

    // Start loading the image
    img.src = imageToTry;
  }, [item.imageUrl, item.amazonUrl, item.name]);

  // Handle delete button click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    if (onDeleteClick) {
      onDeleteClick(e);
    }
  };

  // Handle image loading
  const handleImageLoad = () => {
    setImageLoading(false);
  };

  // Handle image error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    console.error("Image failed to load:", item.imageUrl);

    // Check if the image URL is an object URL (blob:)
    const isObjectUrl = item.imageUrl?.startsWith('blob:');
    if (isObjectUrl) {
      console.warn('Object URL may have expired:', item.imageUrl);
    }

    // Check if it's a Supabase URL
    const isSupabaseUrl = item.imageUrl?.includes('supabase.co/storage/v1/object/public');
    if (isSupabaseUrl) {
      console.warn('Supabase storage URL failed to load:', item.imageUrl);

      // Try to fix common issues with Supabase URLs
      if (item.imageUrl) {
        try {
          // Try to use the URL directly without the Supabase storage prefix
          const urlParts = item.imageUrl.split('/storage/v1/object/public/');
          if (urlParts.length > 1) {
            const directUrl = `${urlParts[0]}/storage/v1/object/public/${urlParts[1]}`;
            console.log('Trying direct URL:', directUrl);
            e.currentTarget.src = directUrl;
            return; // Exit early to give the new URL a chance to load
          }
        } catch (urlError) {
          console.error('Error fixing Supabase URL:', urlError);
        }
      }
    }

    // Check if it's an Amazon URL
    const isAmazonUrl = item.imageUrl?.includes('amazon.com') || item.imageUrl?.includes('amazon.co');
    if (isAmazonUrl) {
      console.warn('Amazon URL failed to load:', item.imageUrl);
      // Try to use the Amazon URL directly
      if (item.amazonUrl) {
        try {
          // Extract the ASIN from the Amazon URL
          const asinMatch = item.amazonUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
          if (asinMatch && asinMatch[1]) {
            const asin = asinMatch[1];
            const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
            console.log('Trying Amazon image URL:', amazonImageUrl);
            e.currentTarget.src = amazonImageUrl;
            return; // Exit early to give the new URL a chance to load
          }
        } catch (urlError) {
          console.error('Error creating Amazon image URL:', urlError);
        }
      }
    }

    // Try to extract ASIN from the item name for Amazon products
    if (!isAmazonUrl && item.name && !item.amazonUrl) {
      try {
        // Look for common Amazon product name patterns
        const asinMatch = item.name.match(/\b([A-Z0-9]{10})\b/);
        if (asinMatch && asinMatch[1]) {
          const asin = asinMatch[1];
          const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
          console.log('Trying Amazon image URL from name:', amazonImageUrl);
          e.currentTarget.src = amazonImageUrl;
          return; // Exit early to give the new URL a chance to load
        }
      } catch (nameError) {
        console.error('Error extracting ASIN from name:', nameError);
      }
    }

    setImageError(true);
    setImageLoading(false);

    // Try to use a placeholder image
    try {
      e.currentTarget.src = "/placeholder.svg";
    } catch (error) {
      console.error('Error setting placeholder image:', error);
      e.currentTarget.src = "https://placehold.co/200?text=No+Image";
    }
  };

  return (
    <GlassCard
      onClick={onClick}
      className={cn(
        "relative transition-all transform cursor-pointer hover:shadow-md",
        selected && "ring-2 ring-primary",
        "h-full flex flex-col"
      )}
    >
      {/* Image section */}
      <div className="relative w-full aspect-square bg-slate-100 rounded-t-lg overflow-hidden">
        {imageSrc && !imageError ? (
          <>
            {imageLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-slate-100">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              </div>
            )}
            <img
              src={imageSrc || ''}
              alt={item.name}
              className="w-full h-full object-contain p-2"
              onLoad={handleImageLoad}
              onError={handleImageError}
              loading="lazy"
              style={{ display: imageLoading ? 'none' : 'block' }}
              crossOrigin="anonymous"
            />
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Package className="w-16 h-16 text-slate-300" />
          </div>
        )}

        {/* Low stock indicator */}
        {lowStock && (
          <div className="absolute bottom-2 right-2 bg-red-100 text-red-600 p-1 rounded-full">
            <AlertTriangle className="h-5 w-5" />
          </div>
        )}

        {/* Delete button */}
        {showDelete && (
          <Button
            size="icon"
            variant="destructive"
            className="absolute top-2 right-2 h-8 w-8 rounded-full opacity-90"
            onClick={handleDeleteClick}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Content section */}
      <div className="p-4 flex-grow flex flex-col">
        <h3 className="font-medium line-clamp-2">{item.name}</h3>

        <div className="mt-2 flex items-center justify-between">
          {/* Quantity display */}
          <div className="flex items-center">
            <div className={cn(
              "px-2 py-0.5 rounded text-sm",
              lowStock ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"
            )}>
              {item.quantity} {item.quantity === 1 ? 'item' : 'items'}
            </div>
          </div>

          {/* Price display */}
          {item.price !== undefined && item.price > 0 && (
            <div className="text-sm font-medium text-slate-700">
              ${item.price.toFixed(2)}
            </div>
          )}
        </div>

        {/* External links */}
        <div className="mt-3 flex items-center gap-2">
          {/* Amazon link */}
          {item.amazonUrl && (
            <a
              href={item.amazonUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-orange-500 hover:text-orange-700 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src="/amazon-icon.svg"
                alt="Amazon"
                className="h-4 w-auto"
              />
            </a>
          )}

          {/* Walmart link */}
          {item.walmartUrl && (
            <a
              href={item.walmartUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src="/walmart-icon.svg"
                alt="Walmart"
                className="h-4 w-auto"
              />
            </a>
          )}
        </div>

        {/* Collection tag */}
        {item.collection && (
          <div className="mt-3 pt-2 border-t border-slate-100">
            <span className="text-xs text-slate-500">
              {item.collection}
            </span>
          </div>
        )}
      </div>
    </GlassCard>
  );
};

export default InventoryCard;
