
import React, { useEffect, useState } from 'react';
import { FormattedInventoryItem } from '@/types/inventory';
import InventoryGrid from './InventoryGrid';
import InventoryTable from './InventoryTable';
import { Button } from '@/components/ui/button';
import { RefreshCw, Grid, List, Package, Search } from 'lucide-react';
import EmptyState from '../ui/EmptyState';
import LoadingState from '../ui/LoadingState';
import ErrorState from '../ui/ErrorState';
import { FilterOptions } from '@/hooks/useInventoryFilters';
import { StandardEmptyState, StandardLoadingState, StandardErrorState, StandardViewToggle } from '@/components/ui/StandardizedUI';

interface InventoryContentProps {
  isLoading: boolean;
  filteredItems: FormattedInventoryItem[];
  onItemClick: (item: FormattedInventoryItem) => void;
  onDeleteItems: (itemIds: string[]) => void;
  searchQuery: string;
  filters: FilterOptions;
  retryFetch: () => void;
  error: unknown;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
}

const InventoryContent: React.FC<InventoryContentProps> = ({
  isLoading,
  filteredItems,
  onItemClick,
  onDeleteItems,
  searchQuery,
  filters,
  retryFetch,
  error,
  viewMode,
  onViewModeChange
}) => {
  // State to track if grid view has been loaded at least once
  const [gridViewLoaded, setGridViewLoaded] = useState(false);

  // When view mode changes to grid, mark it as loaded
  useEffect(() => {
    if (viewMode === 'grid') {
      setGridViewLoaded(true);
    }
  }, [viewMode]);

  // Handle retry button click
  const handleRetryClick = () => {
    retryFetch();
  };

  if (isLoading) {
    return <StandardLoadingState message="Loading inventory items..." />;
  }

  if (error) {
    return (
      <StandardErrorState
        message="We couldn't load your inventory items. Please try again."
        onRetry={handleRetryClick}
        retryLabel="Retry Loading"
      />
    );
  }

  if (filteredItems.length === 0) {
    // If there's a search or filter but no results
    if (searchQuery || filters.property || filters.collection || filters.stockStatus !== 'all') {
      return (
        <StandardEmptyState
          title="No matching items found"
          description="Try adjusting your search or filters"
          icon={<Search className="h-12 w-12 text-muted-foreground" />}
        />
      );
    }

    // If there are no items at all
    return (
      <StandardEmptyState
        title="No inventory items yet"
        description="Add your first inventory item to get started"
        icon={<Package className="h-12 w-12 text-muted-foreground" />}
      />
    );
  }

  return (
    <div>
      <div className="flex justify-end mb-4 space-x-2">
        <StandardViewToggle
          viewType={viewMode}
          onToggleView={onViewModeChange}
        />
        <Button
          variant="outline"
          size="sm"
          onClick={handleRetryClick}
          className="flex items-center gap-1"
        >
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh
        </Button>
      </div>

      {/* Always render the list view, but hide it when grid view is active */}
      <div style={{ display: viewMode === 'list' ? 'block' : 'none' }}>
        <InventoryTable
          items={filteredItems}
          onItemClick={onItemClick}
          onDeleteItems={onDeleteItems}
        />
      </div>

      {/* Always render the grid view if it's been loaded at least once, but hide it when list view is active */}
      {(viewMode === 'grid' || gridViewLoaded) && (
        <div style={{ display: viewMode === 'grid' ? 'block' : 'none' }}>
          <InventoryGrid
            items={filteredItems}
            onItemClick={onItemClick}
            onDeleteItems={onDeleteItems}
          />
        </div>
      )}
    </div>
  );
};

export default InventoryContent;
