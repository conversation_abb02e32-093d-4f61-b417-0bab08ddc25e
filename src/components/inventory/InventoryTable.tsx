
import React, { useState, useEffect } from 'react';
import { FormattedInventoryItem } from '@/types/inventory';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Trash } from 'lucide-react';

interface InventoryTableProps {
  items: FormattedInventoryItem[];
  onItemClick: (item: FormattedInventoryItem) => void;
  onDeleteItems: (itemIds: string[]) => void;
}

const InventoryTable: React.FC<InventoryTableProps> = ({
  items,
  onItemClick,
  onDeleteItems,
}) => {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [imageCache, setImageCache] = useState<Record<string, string>>({});

  // Preload all images
  useEffect(() => {
    // Create a new cache object
    const newCache: Record<string, string> = {};

    // Preload all item images in the background
    items.forEach(item => {
      if (item.imageUrl) {
        // Start with the original URL
        newCache[item.id] = item.imageUrl;

        const img = new Image();
        img.onload = () => {
          // Image loaded successfully, keep the original URL
          newCache[item.id] = item.imageUrl!;
        };

        img.onerror = () => {
          // Try different fallback strategies

          // 1. Check if it's a Supabase URL
          const isSupabaseUrl = item.imageUrl?.includes('supabase.co/storage/v1/object/public');
          if (isSupabaseUrl && item.imageUrl) {
            try {
              // Try to use the URL directly without the Supabase storage prefix
              const urlParts = item.imageUrl.split('/storage/v1/object/public/');
              if (urlParts.length > 1) {
                const directUrl = `${urlParts[0]}/storage/v1/object/public/${urlParts[1]}`;
                console.log('Preloading direct Supabase URL:', directUrl);

                const supabaseImg = new Image();
                supabaseImg.onload = () => {
                  newCache[item.id] = directUrl;
                };
                supabaseImg.src = directUrl;
                return; // Exit early to give the new URL a chance to load
              }
            } catch (urlError) {
              console.error('Error fixing Supabase URL:', urlError);
            }
          }

          // 2. Check if it's an Amazon URL
          const isAmazonUrl = item.imageUrl?.includes('amazon.com') || item.imageUrl?.includes('amazon.co');
          if ((isAmazonUrl || !isSupabaseUrl) && item.amazonUrl) {
            try {
              // Extract the ASIN from the Amazon URL
              const asinMatch = item.amazonUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
              if (asinMatch && asinMatch[1]) {
                const asin = asinMatch[1];
                const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
                console.log('Preloading Amazon image URL:', amazonImageUrl);

                const amazonImg = new Image();
                amazonImg.onload = () => {
                  newCache[item.id] = amazonImageUrl;
                };
                amazonImg.src = amazonImageUrl;
                return; // Exit early to give the new URL a chance to load
              }
            } catch (urlError) {
              console.error('Error creating Amazon image URL:', urlError);
            }
          }

          // If all fallbacks fail, use a placeholder
          newCache[item.id] = "/placeholder.svg";
        };

        // Start loading the image
        img.src = item.imageUrl;
      } else {
        newCache[item.id] = "/placeholder.svg";
      }
    });

    // Update the cache after all images have been processed
    setImageCache(newCache);
  }, [items]);

  const toggleSelectAll = () => {
    if (selectedItems.length === items.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(items.map(item => item.id));
    }
  };

  const toggleSelectItem = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const handleDeleteSelected = () => {
    if (selectedItems.length > 0) {
      onDeleteItems(selectedItems);
      setSelectedItems([]);
    }
  };

  return (
    <div>
      {selectedItems.length > 0 && (
        <div className="flex justify-between mb-2">
          <span className="text-sm">{selectedItems.length} items selected</span>
          <Button variant="destructive" size="sm" onClick={handleDeleteSelected}>
            <Trash className="h-4 w-4 mr-2" />
            Delete Selected
          </Button>
        </div>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={items.length > 0 && selectedItems.length === items.length}
                  onCheckedChange={toggleSelectAll}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>Image</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Property</TableHead>
              <TableHead>Collection</TableHead>
              <TableHead className="text-right">Quantity</TableHead>
              <TableHead className="text-right">Min Quantity</TableHead>
              <TableHead className="text-right">Price</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => (
              <TableRow
                key={item.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => onItemClick(item)}
              >
                <TableCell className="p-2" onClick={(e) => {
                  e.stopPropagation();
                }}>
                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={() => toggleSelectItem(item.id)}
                    aria-label={`Select ${item.name}`}
                  />
                </TableCell>
                <TableCell className="p-2">
                  {(item.imageUrl || imageCache[item.id]) ? (
                    <img
                      src={imageCache[item.id] || item.imageUrl}
                      alt={item.name}
                      className="h-12 w-12 object-cover rounded"
                      onError={(e) => {
                        console.error("Image failed to load:", item.imageUrl);

                        // Check if it's a Supabase URL
                        const isSupabaseUrl = item.imageUrl?.includes('supabase.co/storage/v1/object/public');
                        if (isSupabaseUrl) {
                          try {
                            // Try to use the URL directly without the Supabase storage prefix
                            const urlParts = item.imageUrl?.split('/storage/v1/object/public/') || [];
                            if (urlParts.length > 1) {
                              const directUrl = `${urlParts[0]}/storage/v1/object/public/${urlParts[1]}`;
                              console.log('Trying direct URL:', directUrl);
                              e.currentTarget.src = directUrl;
                              return; // Exit early to give the new URL a chance to load
                            }
                          } catch (urlError) {
                            console.error('Error fixing Supabase URL:', urlError);
                          }
                        }

                        // Check if it's an Amazon URL
                        const isAmazonUrl = item.imageUrl?.includes('amazon.com') || item.imageUrl?.includes('amazon.co');
                        if (isAmazonUrl && item.amazonUrl) {
                          try {
                            // Extract the ASIN from the Amazon URL
                            const asinMatch = item.amazonUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
                            if (asinMatch && asinMatch[1]) {
                              const asin = asinMatch[1];
                              const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
                              console.log('Trying Amazon image URL:', amazonImageUrl);
                              e.currentTarget.src = amazonImageUrl;
                              return; // Exit early to give the new URL a chance to load
                            }
                          } catch (urlError) {
                            console.error('Error creating Amazon image URL:', urlError);
                          }
                        }

                        e.currentTarget.src = "/placeholder.svg";
                      }}
                      crossOrigin="anonymous"
                    />
                  ) : (
                    <div className="h-12 w-12 bg-muted rounded flex items-center justify-center">
                      <span className="text-xs text-muted-foreground">No image</span>
                    </div>
                  )}
                </TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.propertyName}</TableCell>
                <TableCell>{item.collection}</TableCell>
                <TableCell className="text-right">
                  <span className={item.quantity < item.minQuantity ? "text-destructive font-bold" : ""}>
                    {item.quantity}
                  </span>
                </TableCell>
                <TableCell className="text-right">{item.minQuantity}</TableCell>
                <TableCell className="text-right">{item.price ? `$${item.price.toFixed(2)}` : '-'}</TableCell>
              </TableRow>
            ))}
            {items.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default InventoryTable;
