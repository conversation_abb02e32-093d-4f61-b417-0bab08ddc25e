
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Search,
  Plus,
  ShoppingCart,
  Upload,
  Filter,
  RefreshCw,
  Package,
  Trash2
} from 'lucide-react';
import { StandardPageHeader, PageHeaderButtons } from '@/components/ui/StandardizedUI';

interface InventoryHeaderProps {
  onAddItem: () => void;
  onCreateOrder: () => void;
  onBulkImport: () => void;
  setSearchQuery: (query: string) => void;
  searchQuery: string;
  toggleFilters: () => void;
  onRefresh?: () => void;
  isLoading?: boolean;
}

const InventoryHeader: React.FC<InventoryHeaderProps> = ({
  onAddItem,
  onCreateOrder,
  onBulkImport,
  setSearchQuery,
  searchQuery,
  toggleFilters,
  onRefresh,
  isLoading = false
}) => {
  return (
    <StandardPageHeader
      title="Inventory"
      description="Manage your inventory items"
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      searchPlaceholder="Search inventory..."
      onRefresh={onRefresh}
      isLoading={isLoading}
      onToggleFilters={toggleFilters}
      secondaryActions={
        <>
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkImport}
            className="flex items-center gap-1.5"
          >
            <Upload size={16} />
            <span className="hidden sm:inline">Bulk Import</span>
            <span className="sm:hidden">Import</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCreateOrder}
            className="flex items-center gap-1.5"
          >
            <ShoppingCart size={16} />
            <span className="hidden sm:inline">Create Order</span>
            <span className="sm:hidden">Order</span>
          </Button>
        </>
      }
      primaryActionLabel="Add Item"
      onPrimaryAction={onAddItem}
      primaryActionIcon={<Plus size={16} />}
    />
  );
};

export default InventoryHeader;
