import React from 'react';
import {
  Key,
  Bell,
  Palette,
  User,
  Shield,
  HelpCircle,
  Database,
  Paintbrush
} from 'lucide-react';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';
import { useNavigate } from 'react-router-dom';

interface SettingSection {
  id: string;
  label: string;
  description: string;
  icon: React.ElementType;
}

export const SettingsSections: SettingSection[] = [
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    description: 'Configure how you receive notifications'
  },
  {
    id: 'appearance',
    label: 'Appearance',
    icon: Palette,
    description: 'Customize the look and feel of StayFu'
  },

  {
    id: 'account',
    label: 'Account',
    icon: User,
    description: 'Manage your account settings'
  },
  {
    id: 'security',
    label: 'Security',
    icon: Shield,
    description: 'Security and authentication settings'
  },
  {
    id: 'backup',
    label: 'Backup & Export',
    icon: Database,
    description: 'Backup and export your data'
  },
  {
    id: 'api',
    label: 'API & Integrations',
    icon: Key,
    description: 'Manage API tokens and external integrations'
  },
  {
    id: 'style-guide',
    label: 'Style Guide',
    icon: Paintbrush,
    description: 'View UI component standards and guidelines'
  },
  {
    id: 'help',
    label: 'Help & Support',
    icon: HelpCircle,
    description: 'Get help and view documentation'
  }
];

interface SettingsSidebarProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
}

const SettingsSidebar = ({ activeSection, setActiveSection }: SettingsSidebarProps) => {
  const { refreshRouteData } = useNavigationRefresh();
  const navigate = useNavigate();

  const handleSectionChange = (sectionId: string) => {
    // First set the active section
    setActiveSection(sectionId);

    // Then refresh the data for the route
    const path = `/settings/${sectionId}`;
    console.log(`[SettingsSidebar] Navigating to ${path} with data refresh`);
    refreshRouteData(path);

    // Update the URL with hash routing
    navigate(path, { replace: true });

    // Also update the browser URL to include the hash
    const hashedPath = `/#${path}`;
    window.history.replaceState(null, '', hashedPath);
  };

  return (
    <nav className="sticky top-24">
      <ul className="space-y-1">
        {SettingsSections.map((section) => {
          const Icon = section.icon;
          return (
            <li key={section.id}>
              <button
                onClick={() => handleSectionChange(section.id)}
                className={`w-full text-left px-4 py-3 rounded-lg flex items-center gap-3 text-sm font-medium transition-colors ${
                  activeSection === section.id
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted'
                }`}
              >
                <Icon className="h-4 w-4" />
                {section.label}
              </button>
            </li>
          );
        })}
      </ul>
    </nav>
  );
};

export default SettingsSidebar;
