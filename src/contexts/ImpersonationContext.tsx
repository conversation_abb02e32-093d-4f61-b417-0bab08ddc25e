import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from './AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface ImpersonationContextProps {
  isImpersonating: boolean;
  impersonatedUserId: string | null;
  impersonatedUserEmail: string | null;
  impersonatedUserName: string | null;
  applyImpersonationFilter: <T extends { user_id?: string }>(query: any) => any;
}

const ImpersonationContext = createContext<ImpersonationContextProps>({
  isImpersonating: false,
  impersonatedUserId: null,
  impersonatedUserEmail: null,
  impersonatedUserName: null,
  applyImpersonationFilter: (query) => query,
});

export const useImpersonation = () => useContext(ImpersonationContext);

export const ImpersonationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { authState } = useAuth();
  const [impersonatedUserEmail, setImpersonatedUserEmail] = useState<string | null>(null);
  const [impersonatedUserName, setImpersonatedUserName] = useState<string | null>(null);
  const [isImpersonating, setIsImpersonating] = useState<boolean>(false);
  const [impersonatedUserId, setImpersonatedUserId] = useState<string | null>(null);

  // Update impersonation state from authState
  useEffect(() => {
    // Check if impersonation is active from authState
    const impersonationActive = authState?.isImpersonating || false;
    const userId = impersonationActive ? (authState?.user ? authState.user.id : null) : null;

    setIsImpersonating(impersonationActive);
    setImpersonatedUserId(userId);
  }, [authState?.isImpersonating, authState?.user]);

  // Update impersonated user details when impersonation state changes
  useEffect(() => {
    if (isImpersonating && impersonatedUserId) {
      // Get the impersonated user's profile details
      const fetchImpersonatedUserDetails = async () => {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('email, first_name, last_name')
            .eq('id', impersonatedUserId)
            .single();

          if (error) {
            console.error('Error fetching impersonated user details:', error);
            return;
          }

          if (data) {
            setImpersonatedUserEmail(data.email);
            setImpersonatedUserName(`${data.first_name} ${data.last_name}`);
          }
        } catch (error) {
          console.error('Error in fetchImpersonatedUserDetails:', error);
        }
      };

      fetchImpersonatedUserDetails();
    } else {
      // Reset when not impersonating
      setImpersonatedUserEmail(null);
      setImpersonatedUserName(null);
    }
  }, [isImpersonating, impersonatedUserId]);

  // Helper function to apply impersonation filter to any query
  const applyImpersonationFilter = <T extends { user_id?: string }>(query: any): any => {
    if (isImpersonating && impersonatedUserId) {
      console.log(`[ImpersonationContext] Applying impersonation filter for user: ${impersonatedUserId}`);
      return query.eq('user_id', impersonatedUserId);
    }
    return query;
  };

  const contextValue: ImpersonationContextProps = {
    isImpersonating,
    impersonatedUserId,
    impersonatedUserEmail,
    impersonatedUserName,
    applyImpersonationFilter,
  };

  return (
    <ImpersonationContext.Provider value={contextValue}>
      {children}
    </ImpersonationContext.Provider>
  );
};
