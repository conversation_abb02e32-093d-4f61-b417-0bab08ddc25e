# Browser Tools MCP Setup for StayFu

This document provides instructions for setting up and using the Browser Tools MCP for the StayFu project.

## Quick Start

1. Make sure you have the Browser Tools Chrome extension installed
2. Run the launch script to start the Browser Tools Server:
   ```bash
   ./launch-browser-tools-mcp.sh
   ```
3. Keep the terminal window open while using the Browser Tools MCP

## What This Script Does

The `launch-browser-tools-mcp.sh` script:

1. Locates the Browser Tools Server executable
2. Launches it with the correct parameters:
   - Port: 3040 (instead of the default 3025)
   - User data directory: `$HOME/.config/stayfu-browser-tools` (to avoid conflicts with other Chrome instances)

## Troubleshooting

If you encounter issues with the Browser Tools MCP:

1. Check if the Browser Tools Server is running:
   ```bash
   ps aux | grep -i browser-tools-server
   ```

2. Check if port 3040 is in use:
   ```bash
   lsof -i :3040
   ```

3. Make sure the Chrome extension is installed and enabled:
   - Open Chrome and go to `chrome://extensions/`
   - Verify that the BrowserTools extension is enabled

4. Check the console output of the Browser Tools Server for any error messages

## Manual Launch

If the script doesn't work, you can manually start the Browser Tools Server:

```bash
npx @agentdeskai/browser-tools-server@1.2.0 --port 3040 --user-data-dir="$HOME/.config/stayfu-browser-tools"
```

## More Information

For more detailed information about the MCP setup, refer to the [MCP Setup Guide](docs/mcp-setup.md).
