
// Follow the Edge Function Starter
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.32.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get the request body
    const { teamId, email, role, invitedBy, token } = await req.json();
    
    // Validate required parameters
    if (!teamId || !email || !role || !invitedBy || !token) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required parameters',
          details: { teamId, email, role, invitedBy, token }
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders } 
        }
      );
    }

    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing environment variables');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders } 
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('Creating invitation with params:', {
      team_id: teamId,
      email,
      role,
      invited_by: invitedBy,
      token,
      expires_at: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)).toISOString()
    });

    // Insert the invitation directly into the team_invitations table
    const { data, error } = await supabase
      .from('team_invitations')
      .insert({
        team_id: teamId,
        email,
        role,
        invited_by: invitedBy,
        token,
        status: 'pending',
        expires_at: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)).toISOString() // 7 days from now
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error creating invitation:', error);
      return new Response(
        JSON.stringify({ error: error.message, details: error }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders } 
        }
      );
    }

    // Get team name for response
    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .select('name')
      .eq('id', teamId)
      .single();

    if (teamError) {
      console.error('Error fetching team details:', teamError);
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Invitation created successfully',
        data: {
          id: data.id,
          team: teamData || { name: 'Unknown team' }
        }
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders } 
      }
    );
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'Unexpected error occurred', details: error.message }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders } 
      }
    );
  }
});
