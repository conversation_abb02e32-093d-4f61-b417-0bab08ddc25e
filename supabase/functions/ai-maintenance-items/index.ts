
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.2.1";

const GEMINI_API_KEY = Deno.env.get("GEMINI_API_KEY") || "AIzaSyD55Kn_94EdiW7czvu8qZ4G6R76vRL563s";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface MaintenanceRequest {
  text: string;
  properties?: Array<{id: string, name: string}>;
  providers?: Array<{id: string, name: string}>;
}

// Base interface for AI response handlers
interface AiHandler {
  generatePrompt(request: MaintenanceRequest): string;
  parseResponse(responseText: string): unknown;
}

// Handler for maintenance tasks
class MaintenanceTaskHandler implements AiHandler {
  generatePrompt(request: MaintenanceRequest): string {
    const { text, properties = [], providers = [] } = request;
    
    // Include available properties and providers in the prompt
    let propertyContext = "";
    if (properties.length > 0) {
      propertyContext = `Available properties:\n${properties.map(p => `- ${p.name} (ID: ${p.id})`).join('\n')}\n\n`;
    }
    
    let providerContext = "";
    if (providers.length > 0) {
      providerContext = `Available service providers:\n${providers.map(p => `- ${p.name} (ID: ${p.id})`).join('\n')}\n\n`;
    }
    
    return `
      You are a maintenance task analyzer. Your job is to extract potential maintenance tasks from the provided text.
      ${propertyContext}
      ${providerContext}
      For each identified maintenance task, provide the following details in valid JSON format:
      [
        {
          "title": "Brief title of the task",
          "description": "Detailed description of what needs to be done",
          "severity": "low", "medium", "high", or "critical" based on urgency,
          "status": "new" (always set as default),
          "propertyName": "Name of the property if mentioned in the text and matches one of the available properties",
          "propertyId": "ID of the property if it can be identified from the available properties list",
          "dueDate": "A specific due date if mentioned in the format YYYY-MM-DD, or a relative time frame like '2 weeks', '7 days', 'next month'",
          "providerId": "ID of the service provider if one is mentioned and can be matched to the available providers list",
          "providerName": "Name of the service provider if mentioned in the text"
        },
        ...
      ]
      Only return the JSON array, nothing else. Ensure the JSON is valid.
      
      Text to analyze: ${text}
    `;
  }

  parseResponse(responseText: string): unknown {
    // Extract JSON if it's within markdown code blocks or any other formatting
    const jsonMatch = responseText.match(/\[\s*\{.*\}\s*\]/s);
    const jsonText = jsonMatch ? jsonMatch[0] : responseText;
    const parsedResponse = JSON.parse(jsonText);
    
    if (!Array.isArray(parsedResponse)) {
      throw new Error("Response is not an array");
    }
    
    return parsedResponse;
  }
}

// Factory for creating appropriate handlers based on request type
function createHandler(type: string = 'maintenance'): AiHandler {
  switch (type) {
    case 'maintenance':
      return new MaintenanceTaskHandler();
    // Future handlers can be added here
    default:
      return new MaintenanceTaskHandler();
  }
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!GEMINI_API_KEY) {
      throw new Error("GEMINI_API_KEY is not set");
    }

    const requestData: MaintenanceRequest = await req.json();
    const { text, properties, providers } = requestData;

    if (!text || text.trim() === "") {
      throw new Error("Text input is required");
    }

    console.log("Processing text input:", text);
    console.log("Properties provided:", properties?.length || 0);
    console.log("Providers provided:", providers?.length || 0);
    console.log("Using API key:", GEMINI_API_KEY.substring(0, 5) + "...");

    // Initialize the Gemini API
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-flash",
      generationConfig: {
        temperature: 1,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 8192,
      },
    });

    // Get appropriate handler for this request type
    const aiHandler = createHandler();
    
    // Generate prompt using the handler
    const prompt = aiHandler.generatePrompt(requestData);

    console.log("Sending prompt to Gemini API");
    
    // Call the Gemini API
    const result = await model.generateContent(prompt);
    const response = result.response;
    const textResponse = response.text();
    
    console.log("Gemini API response received");
    
    // Process the response to ensure it's valid JSON
    try {
      const parsedResponse = aiHandler.parseResponse(textResponse);
      return new Response(JSON.stringify(parsedResponse), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    } catch (e) {
      console.error("Error parsing Gemini response as JSON:", e);
      console.log("Raw response:", textResponse);
      throw new Error("Failed to parse AI response as JSON");
    }
  } catch (error: any) {
    console.error("Error in AI maintenance items function:", error);
    return new Response(
      JSON.stringify({ 
        error: error.message || "An error occurred while processing the request" 
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);
