import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import { corsHeaders } from '../_shared/cors.ts';
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Function started, parsing request body...')
    console.log('Request method:', req.method)
    console.log('Request headers:', Object.fromEntries(req.headers.entries()))

    const requestBody = await req.json()
    console.log('Request body parsed successfully:', requestBody)

    const { token, email, password, first_name: firstName, last_name: lastName, role } = requestBody
    console.log('Accept invitation direct request:', { token, email, firstName, lastName, role })
    console.log('Supabase URL:', supabaseUrl)
    console.log('Service key available:', !!supabaseServiceKey)
    // Validate required fields
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Invitation token is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
    if (!email) {
      return new Response(JSON.stringify({
        error: 'Email is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
    if (!password) {
      return new Response(JSON.stringify({
        error: 'Password is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
    if (!firstName || !lastName) {
      return new Response(JSON.stringify({
        error: 'First name and last name are required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
    // Get the invitation details
    console.log('Fetching invitation details for token:', token)
    const { data: invitationData, error: invitationError } = await supabaseAdmin.from('team_invitations').select('*').eq('token', token).eq('status', 'pending').single();
    console.log('Invitation query result:', { invitationData, invitationError });
    if (invitationError || !invitationData) {
      console.error('Error fetching invitation:', invitationError);
      return new Response(JSON.stringify({
        error: 'Invalid or expired invitation token'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
    console.log('Invitation data:', invitationData);
    // Check if the email matches the invitation
    if (invitationData.email !== email) {
      return new Response(JSON.stringify({
        error: 'Email does not match the invitation'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
    let userId;
    // First check if the user exists in the auth system by listing users
    const { data: userList, error: userError } = await supabaseAdmin.auth.admin.listUsers();
    if (userError) {
      console.error('Error checking for existing user in auth system:', userError);
    }
    // Find the user by email
    const existingUser = userList?.users?.find((user)=>user.email === email);
    if (existingUser) {
      console.log('User exists in auth system:', existingUser.id);
      userId = existingUser.id;
      // Check if profile exists for this user
      const { data: existingProfile, error: profileError } = await supabaseAdmin.from('profiles').select('id').eq('id', userId).maybeSingle();
      if (profileError) {
        console.error('Error checking for existing profile:', profileError);
      }
      if (!existingProfile) {
        console.log('User exists but profile missing, will create profile');
      }
    }
    // If user doesn't exist, create a new one
    if (!userId) {
      console.log('Creating new user with email:', email);
      try {
        const { data: newUser, error: newUserError } = await supabaseAdmin.auth.admin.createUser({
          email,
          password,
          email_confirm: true,
          user_metadata: {
            first_name: firstName,
            last_name: lastName,
            role: role || invitationData.role || 'service_provider'
          }
        });
        if (newUserError || !newUser?.user) {
          console.error('Error creating new auth user:', newUserError);
          // Check if it's a duplicate user error
          if (newUserError?.message?.includes('already registered') || newUserError?.message?.includes('already exists') || newUserError?.message?.includes('duplicate')) {
            return new Response(JSON.stringify({
              error: 'User already exists',
              details: 'A user with this email address already exists. Please try logging in instead.',
              code: 'USER_ALREADY_EXISTS'
            }), {
              status: 400,
              headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
              }
            });
          }
          return new Response(JSON.stringify({
            error: `Failed to create user: ${newUserError?.message || 'Unknown error'}`,
            details: newUserError?.message || 'Unknown error occurred during user creation'
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          });
        }
        userId = newUser.user.id;
        console.log('New user created with ID:', userId);
      } catch (createUserError) {
        console.error('Exception creating user:', createUserError);
        return new Response(JSON.stringify({
          error: 'Failed to create user account'
        }), {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }
    }
    // Create or update profile if we have a valid user ID
    if (userId) {
      const effectiveRole = role || invitationData.role || 'service_provider';
      try {
        console.log(`Creating/updating profile for user ${userId} using create_profile_safely function`);
        const { data: profileResult, error: createProfileError } = await supabaseAdmin.rpc('create_profile_safely', {
          p_user_id: userId,
          p_first_name: firstName,
          p_last_name: lastName,
          p_role: effectiveRole,
          p_email: email
        });
        if (createProfileError) {
          console.error('Error creating profile with RPC function:', createProfileError);
        // Don't return an error, just log it and continue
        // The profile might be created by the handle_new_user trigger
        } else {
          console.log('Profile creation result:', profileResult);
        }
      } catch (profileError) {
        console.error('Exception when creating profile with RPC function:', profileError);
      // Don't return an error, just log it and continue
      // The profile might be created by the handle_new_user trigger
      }
      // Create service provider profile if the role is service_provider
      if (effectiveRole === 'service_provider') {
        try {
          console.log('Creating service provider profile for user:', userId);
          const { error: spProfileError } = await supabaseAdmin.from('service_providers').upsert({
            id: userId,
            email,
            first_name: firstName,
            last_name: lastName,
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'id'
          });
          if (spProfileError) {
            console.error('Error creating service provider profile:', spProfileError);
          // Don't fail the whole process, just log the error
          } else {
            console.log('Service provider profile created successfully');
          }
        } catch (spError) {
          console.error('Exception creating service provider profile:', spError);
        // Don't fail the whole process, just log the error
        }
      }
    }
    // Accept the invitation and add the user to the team
    console.log('Accepting invitation and adding user to team');
    const { data: acceptResult, error: acceptError } = await supabaseAdmin.rpc('accept_invitation_and_add_member', {
      p_token: token,
      p_user_id: userId
    });
    if (acceptError) {
      console.error('Error accepting invitation:', acceptError);
      return new Response(JSON.stringify({
        error: `Failed to accept invitation: ${acceptError.message}`
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
    console.log('Invitation accepted successfully:', acceptResult);
    return new Response(JSON.stringify({
      success: true,
      user_id: userId,
      team_id: acceptResult?.team_id || invitationData.team_id,
      message: 'Invitation accepted successfully'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Unexpected error in accept-invitation-direct:', error);
    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';
    // Log additional context for debugging
    console.error('Error context:', {
      errorMessage,
      errorStack,
      timestamp: new Date().toISOString()
    });
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: errorMessage,
      message: 'An unexpected error occurred while processing the invitation. Please try again or contact support.',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
});
