-- Create the create_profile_safely RPC function that's missing
CREATE OR R<PERSON>LACE FUNCTION public.create_profile_safely(
  p_user_id UUID,
  p_first_name TEXT,
  p_last_name TEXT,
  p_role TEXT,
  p_email TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_role user_role;
BEGIN
  -- Validate and cast the role
  BEGIN
    v_role := p_role::user_role;
  EXCEPTION WHEN OTHERS THEN
    v_role := 'service_provider'::user_role;
  END;

  -- Try to insert the profile, ignore if it already exists
  BEGIN
    INSERT INTO public.profiles (
      id,
      email,
      first_name,
      last_name,
      role,
      is_super_admin,
      created_at,
      updated_at
    )
    VALUES (
      p_user_id,
      p_email,
      p_first_name,
      p_last_name,
      v_role,
      FALSE,
      NOW(),
      NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
      email = EXCLUDED.email,
      first_name = EXCLUDED.first_name,
      last_name = EXCLUDED.last_name,
      role = EXCLUDED.role,
      updated_at = NOW();

    RETURN jsonb_build_object(
      'success', TRUE,
      'message', 'Profile created or updated successfully'
    );

  EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', FALSE,
      'error', 'Failed to create profile: ' || SQLERRM
    );
  END;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_profile_safely(UUID, TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_profile_safely(UUID, TEXT, TEXT, TEXT, TEXT) TO service_role;
