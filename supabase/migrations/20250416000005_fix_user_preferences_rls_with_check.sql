-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Allow impersonated access to user_preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can create their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can manage their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Admins can manage all preferences" ON public.user_preferences;

-- Create a simplified policy for all operations
CREATE POLICY "Users can manage their own preferences" ON public.user_preferences
    FOR ALL
    TO public
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Create a policy for admins and super admins
CREATE POLICY "Ad<PERSON> can manage all preferences" ON public.user_preferences
    FOR ALL
    TO public
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
        )
    );

-- Update the has_user_access function to be more robust
CREATE OR REPLACE FUNCTION public.has_user_access(p_user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  impersonating_id text;
BEGIN
  -- Handle null user_id
  IF p_user_id IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Super admins and admins have access to all users
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Users have access to their own data
  IF p_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Check if the current user is impersonating the target user
  -- This requires a session variable to be set during impersonation
  BEGIN
    impersonating_id := current_setting('app.impersonating_user_id', TRUE);
    IF impersonating_id = p_user_id::text THEN
      RETURN TRUE;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      -- If the setting doesn't exist, continue with other checks
      NULL;
  END;

  RETURN FALSE;
END;
$$;

COMMENT ON FUNCTION public.has_user_access IS 'Determines if the current user has access to another user''s data. Super admins and admins have access to all users, and users have access to their own data.';
